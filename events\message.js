const handlers = require("../handlers/handlers");
const { QuickDB } = require("quick.db");
const { suggestCommand } = require("../utils/commandSuggest.js");
const db = new QuickDB();

// Cleanup function to remove messages older than 2 hours
async function cleanupOldMessages(groupId) {
  const twoHoursAgo = Date.now() - (2 * 60 * 60 * 1000);

  // Get all keys that start with message_groupId_
  const allKeys = await db.all();
  const messageKeys = allKeys.filter(item =>
    item.id.startsWith(`message_${groupId}_`)
  );

  for (const item of messageKeys) {
    if (item.value && item.value.timestamp < twoHoursAgo) {
      await db.delete(item.id);

      // Also remove from user's message list
      const userMessagesKey = `user_messages_${groupId}_${item.value.userId}`;
      let userMessages = await db.get(userMessagesKey) || [];
      userMessages = userMessages.filter(msgId => msgId !== item.value.id);
      await db.set(userMessagesKey, userMessages);
    }
  }
}

module.exports = async (event, client, blobClient) => {
  //console.log(`${event.message.text} | ${event.message.id} | ${event.source.userId} | ${event.source.type} | ${event.source.groupId}  ` );

  // Store text messages for snipe functionality (only in groups)
  if (event.message.type === "text" && event.source.type === "group" && event.source.groupId) {
    const messageData = {
      id: event.message.id,
      text: event.message.text,
      userId: event.source.userId,
      groupId: event.source.groupId,
      timestamp: Date.now(),
      deleted: false
    };



    // Store message with key: groupId_messageId
    await db.set(`message_${event.source.groupId}_${event.message.id}`, messageData);

    // Also maintain a list of message IDs per user for easier retrieval
    const userMessagesKey = `user_messages_${event.source.groupId}_${event.source.userId}`;
    let userMessages = await db.get(userMessagesKey) || [];
    userMessages.push(event.message.id);

    // Keep only last 20 messages per user to prevent database bloat
    if (userMessages.length > 20) {
      const oldMessageId = userMessages.shift();
      await db.delete(`message_${event.source.groupId}_${oldMessageId}`);
    }

    await db.set(userMessagesKey, userMessages);

    // Periodically cleanup old messages (every 50th message to avoid performance issues)
    if (Math.random() < 0.02) { // 2% chance = roughly every 50 messages
      cleanupOldMessages(event.source.groupId).catch(console.error);
    }
  }

  if (event.source.type === "user" && event.source.userId !== process.env.tama)
    return;

  // Call the centralized handlers
  const isHandled = await handlers(client, blobClient, event);
  if (isHandled) return;

  const userMessage = event.message.text;
  const prefix = "!";

  let isMentionPrefix = false;
  let botMentionDetails = null;

  // Check if the bot is mentioned as a prefix
  // Check if the bot is mentioned at the start of the message
  if (event.message.mention?.mentionees) {
    botMentionDetails = event.message.mention.mentionees.find(
      (mention) =>
        mention.isSelf &&
        event.message.text.substring(0, mention.index).trim() === ""
    );
    isMentionPrefix = !!botMentionDetails;
  }


  // Check if the message only contains the bot mention without a command
  if (isMentionPrefix && userMessage.trim() === "@BaquaBot") {
    return client.replyMessage({
      replyToken: event.replyToken,
      messages: [
        {
          type: "textV2",
          text: "Hai {user}! Aktivasi command pake !command atau @bot command",
          substitution: {
                  user: {
                    type: "mention",
                    mentionee: {
                      type: "user",
                      userId: event.source.userId
                    }
                  }
                }
        },
      ],
    });
  }

  // Variable to check if a prefixed command is handled
  let isCommandHandled = false;

  // Step 1: Check for prefixed commands (either `!` or bot mention)
  if (userMessage.startsWith(prefix) || isMentionPrefix) {
    let args;
    let commandName;

    if (isMentionPrefix) {
      const commandText = userMessage
        .substring(botMentionDetails.index + botMentionDetails.length)
        .trim();
      const allMentions = event.message.mention.mentionees;

      const parts = commandText.split(/ +/).filter(Boolean);
      if (parts.length === 0) {
        commandName = "";
        args = [];
      } else {
        const potentialCommand = parts[0];
        const commandTextStartIndex =
          botMentionDetails.index + botMentionDetails.length;
        // Find the index of the potential command in the *original* message text
        const potentialCommandIndexInFull = userMessage.indexOf(
          potentialCommand,
          commandTextStartIndex
        );

        // Check if the first word after the bot mention is actually another mention
        const isCommandAMention = allMentions.some(
          (m) => m.index === potentialCommandIndexInFull
        );

        if (isCommandAMention) {
          commandName = ""; // It's a mention, not a command
          args = parts;
        } else {
          commandName = parts.shift().toLowerCase();
          args = parts;
        }
      }
    } else {
      args = userMessage.slice(prefix.length).trim().split(/ +/);
      commandName = args.shift()?.toLowerCase();
    }

    // In the command handling section
    const cmd =
    client.commands.get(commandName) ||
    Array.from(client.commands.values()).find( // Changed to Array.find
      (cmd) => cmd.aliases && cmd.aliases.includes(commandName)
    );

    if (cmd && cmd.requiresPrefix !== false) {
      // if(event.source.userId === "U98ea1516be61d07f06d35b6b573d0a5f") {
      //   return client.replyMessage({
      //     replyToken: event.replyToken,
      //     messages: [
      //       {
      //         type: "text",
      //         text: "This user has been restricted from using commands. (📅 2 week left)\n\nReason(s): Unspeficied",
      //         quoteToken: event.message.quoteToken,
      //       }
      //     ]
      //   });
      // }
      try {
        await cmd.handler(client, blobClient, event, args);
        isCommandHandled = true; // Mark that a prefixed command was handled
      } catch (error) {
        console.error(error);
        return client.replyMessage({
          replyToken: event.replyToken,
          messages: [
            {
              type: "text",
              text: `Error executing command: ${error.message || error}`,
              quoteToken: event.message.quoteToken,
            },
          ],
        });
      }
    } else {
      if (!commandName) {
        // If there's no command after prefix/mention, do nothing.
        return;
      }
      // Handle unknown command with suggestion
      const prefixedCommands = new Map(
        Array.from(client.commands.entries()).filter(
          ([, cmd]) => cmd.requiresPrefix !== false
        )
      );
      const suggestion = suggestCommand(commandName, prefixedCommands);
      const replyText = suggestion
        ? `Command not found: ${commandName}\nDid you mean: ${suggestion}?`
        : `Command not found: ${commandName}`;

      return client.replyMessage({
        replyToken: event.replyToken,
        messages: [
          {
            type: "text",
            text: replyText,
            quoteToken: event.message.quoteToken,
          },
        ],
      });
    }
  }

  // Step 2: Handle prefix-less commands only if no prefixed command was handled
  if (!isCommandHandled) {
    client.commands.forEach(async (cmd) => {
      if (!cmd.requiresPrefix) {
        const lowerCaseMessage = userMessage.toLowerCase();
        const shouldActivate = cmd.includes
          ? [
              cmd.command.toLowerCase(),
              ...(cmd.aliases || []).map((alias) => alias.toLowerCase()),
            ].some((keyword) => lowerCaseMessage.includes(keyword))
          : [
              cmd.command.toLowerCase(),
              ...(cmd.aliases || []).map((alias) => alias.toLowerCase()),
            ].includes(lowerCaseMessage.trim());

        if (shouldActivate) {
          if(event.source.userId === "U98ea1516be61d07f06d35b6b573d0a5f") {
            return client.replyMessage({
              replyToken: event.replyToken,
              messages: [
                {
                  type: "text",
                  text: "This user has been restricted from using commands. (📅 1 week left)",
                  quoteToken: event.message.quoteToken,
                }
              ]
            });
          }
          try {
            await cmd.handler(client, blobClient, event, []);
          } catch (error) {
            console.error(error);
            return client.replyMessage({
              replyToken: event.replyToken,
              messages: [
                {
                  type: "text",
                  text: `Error executing command: ${error.message || error}`,
                  quoteToken: event.message.quoteToken,
                },
              ],
            });
          }
        }
      }
    });
  }
};

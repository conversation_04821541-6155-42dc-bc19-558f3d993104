const fs = require("fs");
const Table = require("cli-table3");

module.exports.loadCommands = (client) => {
  // Initialize as Map
  client.commands = new Map();

  const table = new Table({
    head: ["Command", "Category", "Status", "Time Loaded"],
    colWidths: [20, 15, 10, 15],
  });

  const startTime = Date.now();

  fs.readdirSync("./commands").forEach((dirs) => {
    const commands = fs
      .readdirSync(`./commands/${dirs}`)
      .filter((files) => files.endsWith(".js"));

    for (const file of commands) {
      const command = require(`../commands/${dirs}/${file}`);
      const timeLoaded = ((Date.now() - startTime) / 1000).toFixed(2) + "s";

      table.push([
        command.command,
        dirs,
        "Loaded",
        timeLoaded,
      ]);

      // Map.set() works the same as Enmap
      client.commands.set(command.command.toLowerCase(), command);
    }
  });

  console.log(table.toString());
};
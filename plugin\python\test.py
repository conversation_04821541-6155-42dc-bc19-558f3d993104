from g4f.client import Client
from g4f.Provider import <PERSON><PERSON><PERSON>, Gemini, CopilotAccount
import os

import g4f.debug
g4f.debug.logging = True

client = Client(provider=Copilot)
response = client.chat.completions.create(
    # Try "gpt-4o", "deepseek-v3", etc.
    messages=[{"role": "user", "content": "what is my name?"}],
    web_search=False
    
)
print(response.choices[0].message.content)
<!-- MANPAGE: BEGIN EXCLUDED SECTION -->
<div align="center">

![<PERSON><PERSON> Aqua](https://i.ppy.sh/b206739e94b9945173bc7aabfa185c173b288d64/68747470733a2f2f696d6775722d617263686976652e7070792e73682f6b644e323730662e706e67)

# Baquabot ⚓

### Fully Customizable LINE bot built using [line/bot-sdk](https://github.com/line/line-bot-sdk-nodejs)
![LINE Messenger Badge](https://img.shields.io/badge/-LINE%20Messager-limegreen?logo=line&logoColor=white&style=for-the-badge) 
![LINE SDK Badge](https://img.shields.io/badge/-@line/sdk--nodejs%20-red?logo=npm&logoColor=white&style=for-the-badge)

</div>
<!-- MANPAGE: END EXCLUDED SECTION -->


## 📖 About

BaquaBot is a LINE Official Account bot, built using [line/bot-sdk](https://github.com/line/line-bot-sdk-nodejs) intended for my personal use. You can build your own bot using [this template.](https://github.com/Uryaaa/line-bot-template) If you have suggestions, feel free to create an issue or pull request.

## 📃 Features

This bot comes with quite a lot of features that you don't usually see in a LINE bot :

- [x] 🎶 Playing Music and Video from various source supported by [yt-dlp](https://github.com/yt-dlp/yt-dlp)
- [x] 🔊 Music filters
- [x] 🤖 AI Integration
- [x] 🍡 Animanga Lookup
- [ ] ... and more

## 💬 Commands

This bot have around 40 commands and 4 category, see table below for examples 

| **Commands** | **Descriptions**                                | **Categories** |
|--------------|-------------------------------------------------|----------------|
| !mp3         | Download audio from links with optional filters | utility        |  
| !urban       | Search for a word in Urban Dictionary           | fun            |   
| !animes      | Search for anime info                           | anime          |   
| !botinfo     | Shows bot information                           | misc           |  

...and many more!!

## 📃 Requirements
- Nodejs v20.x
- Python v3.13.x
- yt-dlp
- FFmpeg

## 🛠️ Installation
- Clone this repository
- cd to project directory
- ```bash
  $ npm install
  ```
- make a `.env` file and fill
 ```csv
channelAccessToken=     # Bot access token
channelSecret=          # Bot secret channel
baseurl=                # your bot's baseurl 
sirius=                 # your main LINE group id
tama=                   # your user id
serp=                   # serp API key
NGROK_AUTH_TOKEN=       # ngrok auth token
saucenao=               # saucenao API key
IMGFLIP_USERNAME=       # imgflip username
IMGFLIP_PASSWORD=       # imgflip password
HOLODEX_API_KEY=        # holodex.net API key
imgProxy=               # Optional image proxy as LINE does not support webp
cookies=                # Optional nhentai cookies
```
- cd to plugin/python
- run following command in order
  ```bash
  $ pip install virtualenv
  $ python -m venv env
  $ source env/Scripts/activate
  $ pip install -r requirements.txt
- also run if needed
  ```bash
  $ pip freeze | %{$_.split('==')[0]} | %{pip install --upgrade $_}
  ```
- Back to root project directory
- make a folder named `bin`
- put `ytdlp.exe` inside the bin folder
- and finally run
  ```bash
  $ node index.js
  ```
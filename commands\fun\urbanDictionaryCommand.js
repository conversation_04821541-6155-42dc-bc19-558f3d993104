const puppeteer = require("puppeteer");
const cheerio = require("cheerio");

function convertLinks($, el) {
  const cloned = $(el).clone();
  cloned.find("a").each((_, a) => {
    const $a = $(a);
    $a.replaceWith($a.text()); // hanya ambil teks, buang href
  });
  return cloned.text().trim();
}


async function fetchDefinitions(query, page = 1) {
  const url = `https://www.urbandictionary.com/define.php?term=${query}&page=${page}`;
  const browser = await puppeteer.launch({ headless: "new" });
  const pageObj = await browser.newPage();
  await pageObj.goto(url, { waitUntil: "networkidle2" });

  const html = await pageObj.content();
  const $ = cheerio.load(html);
  const definitions = [];

  $("div.p-5.md\\:p-8").each((i, el) => {
    const word = $(el).find("a.word").first().text().trim();
    const meaning = convertLinks($, $(el).find("div.meaning").first());
    const example =
      convertLinks($, $(el).find("div.example").first()) || "No example provided.";

    const contributorEl = $(el).find("div.contributor a").first();
    const contributorName = contributorEl.text().trim();
    const contributorHref = contributorEl.attr("href");

    const contributorDiv = $(el).find("div.contributor");
    const dateNode = contributorDiv
      .contents()
      .filter((i, el) => el.type === "text" && el.prev && el.prev.name === "a")
      .first();
    const date = dateNode ? dateNode.text().trim() : "Unknown date";

    const contributor = `${contributorName} • ${date}`;

    const upvotes =
      $(el).find('button[data-x-bind="thumbUp"] span.text-xs').text().trim() || "0";
    const downvotes =
      $(el).find('button[data-x-bind="thumbDown"] span.text-xs').text().trim() || "0";

    if (word && meaning) {
      definitions.push({
        word,
        meaning,
        example,
        contributor,
        upvotes,
        downvotes,
      });
    }
  });

  await browser.close();
  return definitions;
}

function createBubble(def, index) {
  return {
    type: "bubble",
    size: "hecto",
    header: {
      type: "box",
      layout: "vertical",
      contents: [
        {
          type: "text",
          text: `${def.word} (${index + 1})`,
          weight: "bold",
          size: "lg",
          wrap: true,
        },
      ],
    },
    body: {
      type: "box",
      layout: "vertical",
      spacing: "sm",
      contents: [
        {
          type: "text",
          text: def.meaning.length > 500 ? def.meaning.slice(0, 500) + "…" : def.meaning,
          size: "sm",
          wrap: true,
        },
        {
          type: "text",
          text: `💬 Example: ${def.example.length > 300 ? def.example.slice(0, 300) + "…" : def.example}`,
          size: "xs",
          color: "#555555",
          wrap: true,
          margin: "md",
        },
        {
          type: "text",
          text: `👤 ${def.contributor}`,
          size: "xs",
          color: "#888888",
          wrap: true,
          margin: "md",
        },
        {
          type: "text",
          text: `👍 ${def.upvotes} | 👎 ${def.downvotes}`,
          size: "xs",
          color: "#aaaaaa",
          margin: "sm",
        },
      ],
    },
  };
}

module.exports = {
  command: "urban",
  aliases: ["ub"],
  category: "fun",
  description: "Search for a word in Urban Dictionary",
  requiresPrefix: true,
  includes: false,
  handler: async (client, blobClient, event, args) => {
    if (!args.length) {
      return client.replyMessage({
        replyToken: event.replyToken,
        messages: [
          {
            type: "text",
            text: "Please enter a word to search on Urban Dictionary!\nExample: `!urban simp`",
            quoteToken: event.message.quoteToken,
          },
        ],
      });
    }

    const query = args.join(" ");
    let definitions;
    try {
      definitions = await fetchDefinitions(query);
    } catch (err) {
      console.error("Error scraping Urban Dictionary:", err);
      return client.replyMessage({
        replyToken: event.replyToken,
        messages: [
          {
            type: "text",
            text: "Failed to fetch definitions. Please try again later.",
            quoteToken: event.message.quoteToken,
          },
        ],
      });
    }

    if (!definitions.length) {
      return client.replyMessage({
        replyToken: event.replyToken,
        messages: [
          {
            type: "text",
            text: `No results found for "${query}".`,
            quoteToken: event.message.quoteToken,
          },
        ],
      });
    }

    const bubbles = definitions.slice(0, 10).map(createBubble);
    const flexMessage = {
      type: "flex",
      altText: `Urban Dictionary result for "${query}"`,
      contents: {
        type: "carousel",
        contents: bubbles,
      },
    };

    return client.replyMessage({
      replyToken: event.replyToken,
      messages: [flexMessage],
    });
  },
};

from flask import Blueprint, request, jsonify
from g4f.client import Client
from google import genai
import requests, os

image_bp = Blueprint('image', __name__)
genai_client = genai.Client(api_key=os.getenv("GOOGLE_API_KEY"))

@image_bp.route('/image', methods=['POST'])
def generate_image():
    try:
        prompt = request.get_json().get("prompt")
        if not prompt:
            return jsonify({"error": "Prompt is required"}), 400

        client = Client()
        response = client.images.generate(
            model="flux",
            prompt=prompt,
            response_format="url"
        )
        return jsonify({"url": response.data[0].url})
    except Exception as e:
        return jsonify({"error": str(e)}), 500

@image_bp.route('/analyze_image', methods=['POST'])
def analyze_image():
    try:
        data = request.get_json()
        image_url = data.get("image_url")
        question = data.get("question", "What is in this image?")
        if not image_url:
            return jsonify({"error": "Image URL is required"}), 400

        img_data = requests.get(image_url).content
        with open("temp_image.png", "wb") as f:
            f.write(img_data)

        uploaded_file = genai_client.files.upload(file="temp_image.png")
        response = genai_client.models.generate_content(
            model="gemini-2.5-flash",
            contents=[uploaded_file, question]
        )
        return jsonify({"answer": response.text})
    except Exception as e:
        return jsonify({"error": str(e)}), 500
    finally:
        if os.path.exists("temp_image.png"):
            os.remove("temp_image.png")

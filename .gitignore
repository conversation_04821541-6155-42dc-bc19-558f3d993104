# Git - Keep this out of other repos
.git

# Node.js
node_modules/
package-lock.json
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Python
__pycache__/
.venv/
venv/
env/
uv.lock

# Databases
# quick.db default
json.sqlite
chat_history.db
# better-sqlite3 / enmap
data/

# Logs
logs/
*.log
app.log

# Local Environment
.env
.env.local
.env.*.local

# Puppeteer
.local-chromium/
har_and_cookies/

# Build output / Temp
bin/
static/
.yoyo/

# OS generated files
.DS_Store
Thumbs.db

# Editor specific
.vscode/
.idea/
.http

# Misc
start.bat
*.png
tests/
*.json
*.txt

/**
 * @fileoverview A utility to parse human-readable file size strings into bytes.
 * Handles common binary (KiB, MiB) and decimal (KB, MB) units.
 */

/**
 * Converts a file size string (e.g., "250.5 MiB", "1.2 GB") into a numerical value in bytes.
 * This function is designed to parse the output of command-line tools like yt-dlp.
 * It handles both binary (MiB, GiB) and decimal (MB, GB) units, treating them as binary (base 1024)
 * for a conservative calculation, as that's most common for file sizes.
 *
 * @param {string} sizeString The human-readable file size string to parse.
 * @returns {number} The total size in bytes. Returns 0 if the string is invalid or cannot be parsed.
 *
 * @example
 * // Returns 1048576
 * parseSizeToBytes("1.00MiB");
 *
 * @example
 * // Returns 262144000
 * parseSizeToBytes("250 MiB");
 *
 * @example
 * // Returns 1288490188.8
 * parseSizeToBytes("1.2 GiB");
 *
 * @example
 * // Returns 0 for invalid input
 * parseSizeToBytes("Invalid string");
 */
function parseSizeToBytes(sizeString) {
  // Return 0 for any invalid or empty input to prevent errors.
  if (!sizeString || typeof sizeString !== 'string') {
    return 0;
  }

  // Regular expression to extract the numerical value and the unit.
  // It's case-insensitive (/i) to match 'mib', 'MiB', 'MIB', etc.
  const sizeMatch = sizeString.match(/([\d\.]+)\s*(\w+)/);

  // If the string doesn't match the expected "value unit" format, return 0.
  if (!sizeMatch) {
    return 0;
  }

  const size = parseFloat(sizeMatch[1]);
  const unit = sizeMatch[2].toUpperCase();

  // A map of units to their respective multiplier in bytes (base 1024).
  const units = {
    'B': 1,
    'BYTES': 1,
    'KIB': 1024,
    'KB': 1024, // Treat KB as KiB for this use case
    'MIB': 1024 ** 2,
    'MB': 1024 ** 2, // Treat MB as MiB
    'GIB': 1024 ** 3,
    'GB': 1024 ** 3, // Treat GB as GiB
    'TIB': 1024 ** 4,
    'TB': 1024 ** 4, // Treat TB as TiB
  };

  // Get the multiplier for the detected unit, or default to 1 if the unit is unknown.
  const multiplier = units[unit] || 1;

  return size * multiplier;
}

// Export the function to be used in other modules.
module.exports = {
  parseSizeToBytes
};

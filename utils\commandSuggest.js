/**
 * Computes the Dice Coefficient between two strings.
 * It measures similarity based on overlapping bigrams.
 *
 * @param {string} str1 - The first string.
 * @param {string} str2 - The second string.
 * @returns {number} - A similarity score between 0 (no match) and 1 (exact match).
 */
function diceCoefficient(str1, str2) {
    if (!str1 || !str2) return 0;
    if (str1 === str2) return 1;
    if (str1.length < 2 || str2.length < 2) return 0;

    const bigrams = (s) => new Set(Array.from({ length: s.length - 1 }, (_, i) => s.slice(i, i + 2)));

    const bigramSet1 = bigrams(str1);
    const bigramSet2 = bigrams(str2);

    const intersectionSize = [...bigramSet1].filter(bigram => bigramSet2.has(bigram)).length;
    return (2 * intersectionSize) / (bigramSet1.size + bigramSet2.size);
}

/**
 * Suggests the closest matching command based on input similarity.
 *
 * @param {string} input - The input command name.
 * @param {Map<string, { aliases?: string[] }>} commands - The commands map.
 * @returns {string|null} - The best-matching command name or null if no match is found.
 */
function suggestCommand(input, commands) {
    let bestMatch = null;
    let highestScore = 0;
    const SIMILARITY_THRESHOLD = 0.3;

    for (const [name, { aliases = [] }] of commands) {
        const scores = [diceCoefficient(input, name), ...aliases.map(alias => diceCoefficient(input, alias))];
        const maxScore = Math.max(...scores);

        if (maxScore > highestScore) {
            highestScore = maxScore;
            bestMatch = name;
        }
    }

    return highestScore >= SIMILARITY_THRESHOLD ? bestMatch : null;
}

module.exports = { diceCoefficient, suggestCommand };

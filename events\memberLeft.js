const { QuickDB } = require("quick.db");
const db = new QuickDB();
const { clearSessionTimeout } = require("../utils/sessionTimeouts");

module.exports = async (event, client, blobClient) => {
  const groupId = event.source.groupId;
  const leftUserIds = event.left.members.map((member) => member.userId);

  for (const userId of leftUserIds) {
    console.log(`User ${userId} left group ${groupId}. Checking for active sessions.`);

    // Check and clear Trivia
    if ((await db.get(`${groupId}_triviaOwner`)) === userId) {
      await db.delete(`${groupId}_triviaActive`);
      await db.delete(`${groupId}_triviaOwner`);
      await db.delete(`${groupId}_triviaState`);
      clearSessionTimeout(groupId);
      console.log(`Cleared trivia session for user ${userId} in group ${groupId}.`);
    }

    // Check and clear Akinator
    if ((await db.get(`${groupId}_akinatorOwner`)) === userId) {
      await db.delete(`${groupId}_akinatorActive`);
      await db.delete(`${groupId}_akinatorOwner`);
      await db.delete(`${groupId}_akinatorSession`);
      await db.delete(`${groupId}_akinatorStep`);
      clearSessionTimeout(groupId);
      console.log(`Cleared akinator session for user ${userId} in group ${groupId}.`);
    }

    // Check and clear Word Guess
    if ((await db.get(`${groupId}_wordguessOwner`)) === userId) {
      await db.delete(`${groupId}_wordguessActive`);
      await db.delete(`${groupId}_wordguessOwner`);
      await db.delete(`${groupId}_wordguessState`);
      clearSessionTimeout(groupId);
      console.log(`Cleared wordguess session for user ${userId} in group ${groupId}.`);
    }

    // Check and clear Hirasen
    if ((await db.get(`${groupId}_hirasenOwner`)) === userId) {
      await db.delete(`${groupId}_hirasenActive`);
      await db.delete(`${groupId}_hirasenOwner`);
      await db.delete(`${groupId}_hirasenState`);
      clearSessionTimeout(groupId);
      console.log(`Cleared hirasen session for user ${userId} in group ${groupId}.`);
    }

    // For user-specific sessions (not group-based)
    if (await db.get(`saucenao_${userId}_active`)) {
      await db.delete(`saucenao_${userId}_active`);
      clearSessionTimeout(userId);
      console.log(`Cleared saucenao session for user ${userId}.`);
    }
    if (await db.get(`traceMoe_${userId}_active`)) {
      await db.delete(`traceMoe_${userId}_active`);
      clearSessionTimeout(userId);
      console.log(`Cleared traceMoe session for user ${userId}.`);
    }
    if ((await db.get(`echoMode_owner`)) === userId) {
      await db.delete(`echoMode_owner`);
      clearSessionTimeout(userId);
      console.log(`Cleared echo mode session for user ${userId}.`);
    }
  }

  // Original functionality to announce departure
  const usernames = await Promise.all(
    event.left.members.map(async (member) => {
      try {
        const userProfile = await client.getGroupMemberProfile(
          groupId,
          member.userId
        );
        return userProfile.displayName;
      } catch (error) {
        console.error(
          `Failed to get user profile for user ID ${member.userId}:`,
          error
        );
        return "Seseorang"; // Default name if the profile fetch fails
      }
    })
  );

  const messageText = `${usernames.join(
    ", "
  )} telah meninggalkan grup, selamat tinggal! Semoga bisa bertemu lagi`;

  await client.replyMessage({
    replyToken: event.replyToken,
    messages: [
      {
        type: "text",
        text: messageText,
      },
    ],
  });
};

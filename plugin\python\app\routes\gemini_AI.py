from flask import Blueprint, request, jsonify
from google import genai
from google.genai import types
import os
import logging
import json
import sqlite3

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

gemini_ai_bp = Blueprint('gemini_ai', __name__)

# Database setup for chat history
DB_PATH = os.path.join(os.path.dirname(__file__), '..', '..', 'chat_history.db')

def init_db():
    """Initialize the chat history database."""
    conn = sqlite3.connect(DB_PATH)
    cursor = conn.cursor()
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS chat_history (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            user_id TEXT NOT NULL,
            messages TEXT NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
    ''')
    conn.commit()
    conn.close()

def get_chat_history(user_id, max_messages=40):
    """Get chat history for a user."""
    try:
        conn = sqlite3.connect(DB_PATH)
        cursor = conn.cursor()
        cursor.execute('SELECT messages FROM chat_history WHERE user_id = ?', (user_id,))
        result = cursor.fetchone()
        conn.close()

        if result:
            messages = json.loads(result[0])
            # Keep only the last max_messages
            if len(messages) > max_messages:
                messages = messages[-max_messages:]
            return messages
        return []
    except Exception as e:
        logger.error(f"Error getting chat history: {str(e)}")
        return []

def save_chat_history(user_id, messages):
    """Save chat history for a user."""
    try:
        conn = sqlite3.connect(DB_PATH)
        cursor = conn.cursor()

        # Check if user exists
        cursor.execute('SELECT id FROM chat_history WHERE user_id = ?', (user_id,))
        exists = cursor.fetchone()

        messages_json = json.dumps(messages)

        if exists:
            cursor.execute('''
                UPDATE chat_history
                SET messages = ?, updated_at = CURRENT_TIMESTAMP
                WHERE user_id = ?
            ''', (messages_json, user_id))
        else:
            cursor.execute('''
                INSERT INTO chat_history (user_id, messages)
                VALUES (?, ?)
            ''', (user_id, messages_json))

        conn.commit()
        conn.close()
        return True
    except Exception as e:
        logger.error(f"Error saving chat history: {str(e)}")
        return False

# Initialize database on import
init_db()

def create_gemini_client():
    """Create and return a Gemini AI client using the genai library."""
    try:
        api_key = os.getenv('GOOGLE_API_KEY')
        if not api_key:
            raise ValueError("GEMINI_API_KEY environment variable is required")
        
        client = genai.Client(api_key=api_key)
        return client
    except Exception as e:
        logger.error(f"Failed to create Gemini client: {str(e)}")
        raise

@gemini_ai_bp.route('/gemini/chat-with-history', methods=['POST'])
def gemini_chat_with_history():
    """
    Chat endpoint with database history support (compatible with gptChatCommand).

    Expected JSON payload:
    {
        "user_id": "unique_user_identifier",
        "message": "Hello, how are you?",
        "system_prompt": "You are a helpful assistant...",  // optional
        "model": "gemini-2.5-flash",  // optional
        "temperature": 0.7,           // optional
        "max_tokens": 1000           // optional
    }
    """
    try:
        data = request.json
        if not data:
            return jsonify({"error": "No JSON data provided"}), 400

        user_id = data.get("user_id")
        message = data.get("message")

        if not user_id or not message:
            return jsonify({"error": "user_id and message are required"}), 400

        # Extract configuration
        system_prompt = data.get("system_prompt", "You are a helpful AI assistant.")
        model = data.get("model", "gemini-2.5-flash")
        temperature = data.get("temperature", 0.7)
        max_tokens = data.get("max_tokens", 1000)

        # Get chat history from database
        previous_messages = get_chat_history(user_id)

        # Add new user message
        previous_messages.append({
            "role": "user",
            "content": message
        })

        # Keep only last 40 messages to prevent context overflow
        if len(previous_messages) > 40:
            previous_messages = previous_messages[-40:]

        # Create client
        client = create_gemini_client()

        # Prepare messages for Gemini
        contents = []

        # Add system prompt as first message
        if system_prompt:
            contents.append(types.UserContent(
                parts=[types.Part.from_text(text=system_prompt)]
            ))
            contents.append(types.ModelContent(
                parts=[types.Part.from_text(text="I understand. I'll follow these instructions.")]
            ))

        # Convert message history to Gemini format
        for msg in previous_messages:
            role = msg.get("role", "user")
            content = msg.get("content", "")

            if role == "user":
                contents.append(types.UserContent(
                    parts=[types.Part.from_text(text=content)]
                ))
            elif role == "assistant" or role == "model":
                contents.append(types.ModelContent(
                    parts=[types.Part.from_text(text=content)]
                ))

        # Generate response
        response = client.models.generate_content(
            model=model,
            contents=contents,
            config=types.GenerateContentConfig(
                temperature=temperature,
                max_output_tokens=max_tokens,
            ),
        )

        reply_text = response.text

        # Add assistant response to history
        previous_messages.append({
            "role": "assistant",
            "content": reply_text
        })

        # Save updated history to database
        save_chat_history(user_id, previous_messages)

        # Handle usage metadata safely
        usage_info = {"prompt_tokens": 0, "completion_tokens": 0, "total_tokens": 0}
        if hasattr(response, 'usage_metadata') and response.usage_metadata:
            usage_metadata = response.usage_metadata

            # Debug: log available attributes
            logger.info(f"Usage metadata attributes: {dir(usage_metadata)}")

            # Check for different possible attribute names
            usage_info["prompt_tokens"] = getattr(usage_metadata, 'prompt_token_count',
                                                getattr(usage_metadata, 'input_tokens', 0))
            usage_info["completion_tokens"] = getattr(usage_metadata, 'candidates_token_count',
                                                    getattr(usage_metadata, 'output_tokens', 0))
            usage_info["total_tokens"] = getattr(usage_metadata, 'total_token_count',
                                                getattr(usage_metadata, 'total_tokens', 0))

        return jsonify({
            "response": reply_text,
            "model": model,
            "usage": usage_info,
            "history_length": len(previous_messages)
        })

    except ValueError as ve:
        logger.error(f"Configuration error: {str(ve)}")
        return jsonify({"error": f"Configuration error: {str(ve)}"}), 400
    except Exception as e:
        logger.error(f"Error in gemini_chat_with_history: {str(e)}")
        return jsonify({"error": f"Internal server error: {str(e)}"}), 500

@gemini_ai_bp.route('/gemini/chat', methods=['POST'])
def gemini_chat():
    """
    Chat endpoint using Google's Gemini AI models via genai client.
    
    Expected JSON payload:
    {
        "messages": [
            {"role": "user", "content": "Hello, how are you?"}
        ],
        "model": "gemini-2.5-flash",  // optional, defaults to gemini-2.5-flash
        "temperature": 0.7,           // optional, defaults to 0.7
        "max_tokens": 1000           // optional, defaults to 1000
    }
    """
    try:
        data = request.json
        if not data:
            return jsonify({"error": "No JSON data provided"}), 400
        
        messages = data.get("messages")
        if not messages:
            return jsonify({"error": "Messages are required"}), 400
        
        # Extract configuration
        model = data.get("model", "gemini-2.5-flash")
        temperature = data.get("temperature", 0.7)
        max_tokens = data.get("max_tokens", 1000)
        
        # Create client
        client = create_gemini_client()
        
        # Convert messages to Gemini format
        contents = []
        for msg in messages:
            role = msg.get("role", "user")
            content = msg.get("content", "")
            
            if role == "user":
                contents.append(types.UserContent(
                    parts=[types.Part.from_text(text=content)]
                ))
            elif role == "assistant" or role == "model":
                contents.append(types.ModelContent(
                    parts=[types.Part.from_text(text=content)]
                ))
            else:
                # Default to user content for unknown roles
                contents.append(types.UserContent(
                    parts=[types.Part.from_text(text=content)]
                ))
        
        # Generate response
        response = client.models.generate_content(
            model=model,
            contents=contents,
            config=types.GenerateContentConfig(
                temperature=temperature,
                max_output_tokens=max_tokens,
            ),
        )
        
        # Handle usage metadata safely
        usage_info = {"prompt_tokens": 0, "completion_tokens": 0, "total_tokens": 0}
        if hasattr(response, 'usage_metadata') and response.usage_metadata:
            usage_metadata = response.usage_metadata

            # Debug: log available attributes (only for regular chat to avoid spam)
            logger.debug(f"Usage metadata attributes: {dir(usage_metadata)}")

            # Check for different possible attribute names
            usage_info["prompt_tokens"] = getattr(usage_metadata, 'prompt_token_count',
                                                getattr(usage_metadata, 'input_tokens', 0))
            usage_info["completion_tokens"] = getattr(usage_metadata, 'candidates_token_count',
                                                    getattr(usage_metadata, 'output_tokens', 0))
            usage_info["total_tokens"] = getattr(usage_metadata, 'total_token_count',
                                                getattr(usage_metadata, 'total_tokens', 0))

        return jsonify({
            "response": response.text,
            "model": model,
            "usage": usage_info
        })
        
    except ValueError as ve:
        logger.error(f"Configuration error: {str(ve)}")
        return jsonify({"error": f"Configuration error: {str(ve)}"}), 400
    except Exception as e:
        logger.error(f"Error in gemini_chat: {str(e)}")
        return jsonify({"error": f"Internal server error: {str(e)}"}), 500

@gemini_ai_bp.route('/gemini/stream', methods=['POST'])
def gemini_stream():
    """
    Streaming chat endpoint using Google's Gemini AI models.
    
    Expected JSON payload:
    {
        "message": "Tell me a story",
        "model": "gemini-2.5-flash",  // optional
        "temperature": 0.7            // optional
    }
    """
    try:
        data = request.json
        if not data:
            return jsonify({"error": "No JSON data provided"}), 400
        
        message = data.get("message")
        if not message:
            return jsonify({"error": "Message is required"}), 400
        
        model = data.get("model", "gemini-2.5-flash")
        temperature = data.get("temperature", 0.7)
        
        # Create client
        client = create_gemini_client()
        
        def generate():
            try:
                for chunk in client.models.generate_content_stream(
                    model=model,
                    contents=message,
                    config=types.GenerateContentConfig(
                        temperature=temperature,
                        max_output_tokens=1000,
                    ),
                ):
                    if chunk.text:
                        yield f"data: {chunk.text}\n\n"
                yield "data: [DONE]\n\n"
            except Exception as e:
                logger.error(f"Error in streaming: {str(e)}")
                yield f"data: Error: {str(e)}\n\n"
        
        return generate(), 200, {
            'Content-Type': 'text/plain; charset=utf-8',
            'Cache-Control': 'no-cache',
            'Connection': 'keep-alive',
        }
        
    except Exception as e:
        logger.error(f"Error in gemini_stream: {str(e)}")
        return jsonify({"error": f"Internal server error: {str(e)}"}), 500

@gemini_ai_bp.route('/gemini/models', methods=['GET'])
def list_models():
    """List available Gemini models."""
    try:
        client = create_gemini_client()
        models = []
        
        for model in client.models.list():
            models.append({
                "name": model.name,
                "display_name": getattr(model, 'display_name', model.name),
                "description": getattr(model, 'description', ''),
                "input_token_limit": getattr(model, 'input_token_limit', 0),
                "output_token_limit": getattr(model, 'output_token_limit', 0),
            })
        
        return jsonify({
            "models": models,
            "recommended_free_models": [
                "gemini-2.5-flash",
                "gemini-2.5-flash-lite", 
                "gemini-2.0-flash",
                "gemini-2.0-flash-lite"
            ]
        })
        
    except Exception as e:
        logger.error(f"Error listing models: {str(e)}")
        return jsonify({"error": f"Failed to list models: {str(e)}"}), 500

@gemini_ai_bp.route('/gemini/health', methods=['GET'])
def health_check():
    """Health check endpoint for Gemini AI service."""
    try:
        client = create_gemini_client()
        
        # Test with a simple request
        response = client.models.generate_content(
            model="gemini-2.5-flash",
            contents="Hello",
            config=types.GenerateContentConfig(
                max_output_tokens=10,
            ),
        )
        
        return jsonify({
            "status": "healthy",
            "service": "Gemini AI",
            "client": "google-genai",
            "test_response": response.text[:50] + "..." if len(response.text) > 50 else response.text
        })
        
    except Exception as e:
        logger.error(f"Health check failed: {str(e)}")
        return jsonify({
            "status": "unhealthy",
            "service": "Gemini AI",
            "error": str(e)
        }), 500

@gemini_ai_bp.route('/gemini/clear-history', methods=['POST'])
def clear_chat_history():
    """Clear chat history for a specific user."""
    try:
        data = request.json
        if not data:
            return jsonify({"error": "No JSON data provided"}), 400

        user_id = data.get("user_id")
        if not user_id:
            return jsonify({"error": "user_id is required"}), 400

        conn = sqlite3.connect(DB_PATH)
        cursor = conn.cursor()
        cursor.execute('DELETE FROM chat_history WHERE user_id = ?', (user_id,))
        deleted_count = cursor.rowcount
        conn.commit()
        conn.close()

        return jsonify({
            "success": True,
            "message": f"Chat history cleared for user {user_id}",
            "deleted_records": deleted_count
        })

    except Exception as e:
        logger.error(f"Error clearing chat history: {str(e)}")
        return jsonify({"error": f"Failed to clear history: {str(e)}"}), 500

const axios = require("axios");
const utils = require("../utils/utils")
module.exports = {
  postbackData: "characterData=", // Prefix for dynamic sticker postbacks
  cooldown: 10,
  handler: async (client, blobClient, event, id) => {
    
    const characterFullById = await axios(`https://api.jikan.moe/v4/characters/${id}/full`)
    const replyText =
      `Name: ${characterFullById.data.data.name || "None"}\n` +
      `Japanese: ${characterFullById.data.data.name_kanji || "None"}\n` +
      `Nicknames: ${characterFullById.data.data.nicknames.length > 0 ? characterFullById.data.data.nicknames.join(", ") : "None"}\n` +
      `About: ${characterFullById.data.data.about ? utils.TruncateText(characterFullById.data.data.about, 2000) : "No biography written"}\n`;

    const replyText2 = 
      `Anime: ${characterFullById.data.data.anime.length > 0 ? characterFullById.data.data.anime.map((anime) => `${anime.anime.title || "None"} (${anime.role || "None"})`).join("\n") : "None"}\n\n` +
      `Manga: ${characterFullById.data.data.manga.length > 0 ? characterFullById.data.data.manga.map((manga) => `${manga.manga.title || "None"} (${manga.role || "None"})`).join("\n") : "None"}\n\n` +
      `Voices: ${characterFullById.data.data.voices.length > 0 ? characterFullById.data.data.voices.map((voice) => `${voice.person.name || "None"} (${voice.language || "None"})`).join("\n") : "None"}\n` +
      `More: ${characterFullById.data.data.url || "None"}`;


      
    client.replyMessage({
      replyToken: event.replyToken,
      messages: [
        {
          type: "image",
          originalContentUrl: characterFullById.data.data.images.jpg.image_url,
          previewImageUrl: characterFullById.data.data.images.jpg.image_url,
        },
        {
          type: "text",
          text: replyText,
        },
        {
          type: "text",
          text: replyText2,
        },
      ],
    });
  },
};

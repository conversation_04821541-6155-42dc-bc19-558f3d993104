const { QuickDB } = require("quick.db");
const db = new QuickDB();

// Store pending prompt edits with timeout
const pendingEdits = new Map();

module.exports = {
  command: "sysprompt",
  aliases: ["systemprompt", "prompt"],
  category: "aichat",
  description: "Mengatur system prompt personal untuk chat",
  requiresPrefix: true,
  includes: false,
  handler: async (client, blobClient, event, args) => {
    let userId = event.source.userId;
    if (event.source.type === "group") {
      userId = `${event.source.groupId}:${event.source.userId}`;
    }

    let user;
    if (event.source.type === "group") {
      user = await client.getGroupMemberProfile(event.source.groupId, event.source.userId);
    } else {
      user = await client.getProfile(event.source.userId);
    }

    const userName = user.displayName;

    // Check if user wants to use default prompt
    if (args.length > 0 && args[0].toLowerCase() === "/default") {
      await db.delete(`customSystemPrompt_${userId}`);
      await client.replyMessage({
        replyToken: event.replyToken,
        messages: [
          {
            type: "text",
            text: `System prompt untuk ${userName} telah direset ke default BaquaBot.`,
            quoteToken: event.message.quoteToken,
          }
        ]
      });
      return;
    }

    // Check if user wants to cancel pending edit
    if (args.length > 0 && args[0].toLowerCase() === "/cancel") {
      if (pendingEdits.has(userId)) {
        clearTimeout(pendingEdits.get(userId).timeout);
        pendingEdits.delete(userId);
        await client.replyMessage({
          replyToken: event.replyToken,
          messages: [
            {
              type: "text",
              text: "Pengeditan system prompt dibatalkan.",
              quoteToken: event.message.quoteToken,
            }
          ]
        });
      } else {
        await client.replyMessage({
          replyToken: event.replyToken,
          messages: [
            {
              type: "text",
              text: "Tidak ada pengeditan system prompt yang sedang berlangsung.",
              quoteToken: event.message.quoteToken,
            }
          ]
        });
      }
      return;
    }

    // If no arguments, show current status and start editing process
    if (args.length === 0) {
      const currentPrompt = await db.get(`customSystemPrompt_${userId}`);
      
      if (currentPrompt) {
        await client.replyMessage({
          replyToken: event.replyToken,
          messages: [
            {
              type: "text",
              text: `${userName}, kamu saat ini menggunakan system prompt personal.\n\nBalas pesan ini dengan prompt baru dalam 10 menit atau ketik /cancel untuk membatalkan.\n\nGunakan /default untuk kembali ke prompt default BaquaBot.`,
              quoteToken: event.message.quoteToken,
            }
          ]
        });
      } else {
        await client.replyMessage({
          replyToken: event.replyToken,
          messages: [
            {
              type: "text",
              text: `${userName}, kamu saat ini menggunakan system prompt default BaquaBot.\n\nBalas pesan ini dengan prompt personal dalam 10 menit atau ketik /cancel untuk membatalkan.\n\nGunakan /default untuk tetap menggunakan prompt default.`,
              quoteToken: event.message.quoteToken,
            }
          ]
        });
      }

      // Set up pending edit with 10 minute timeout
      if (pendingEdits.has(userId)) {
        clearTimeout(pendingEdits.get(userId).timeout);
      }

      const timeout = setTimeout(async () => {
        pendingEdits.delete(userId);
        // Note: We can't send a timeout message here because we don't have replyToken
        // The timeout will just silently expire
      }, 10 * 60 * 1000); // 10 minutes

      pendingEdits.set(userId, {
        timeout: timeout,
        messageId: event.message.id
      });

      return;
    }

    // If user provided arguments directly, treat as new prompt
    const newPrompt = args.join(" ");
    await db.set(`customSystemPrompt_${userId}`, newPrompt);
    
    await client.replyMessage({
      replyToken: event.replyToken,
      messages: [
        {
          type: "text",
          text: `System prompt untuk ${userName} berhasil diubah!\n\nUntuk mengeditnya lagi gunakan !sysprompt atau !sysprompt /default untuk kembali ke default.`,
          quoteToken: event.message.quoteToken,
        }
      ]
    });
  },

  // Handle replies to sysprompt messages
  handleReply: async (client, event, originalMessage) => {
    let userId = event.source.userId;
    if (event.source.type === "group") {
      userId = `${event.source.groupId}:${event.source.userId}`;
    }

    // Check if this user has a pending edit
    if (!pendingEdits.has(userId)) {
      return false; // Not handling this reply
    }

    const pendingEdit = pendingEdits.get(userId);
    clearTimeout(pendingEdit.timeout);
    pendingEdits.delete(userId);

    let user;
    if (event.source.type === "group") {
      user = await client.getGroupMemberProfile(event.source.groupId, event.source.userId);
    } else {
      user = await client.getProfile(event.source.userId);
    }

    const userName = user.displayName;
    const newPrompt = event.message.text;

    // Check for special commands
    if (newPrompt.toLowerCase() === "/cancel") {
      await client.replyMessage({
        replyToken: event.replyToken,
        messages: [
          {
            type: "text",
            text: "Pengeditan system prompt dibatalkan.",
            quoteToken: event.message.quoteToken,
          }
        ]
      });
      return true;
    }

    if (newPrompt.toLowerCase() === "/default") {
      await db.delete(`customSystemPrompt_${userId}`);
      await client.replyMessage({
        replyToken: event.replyToken,
        messages: [
          {
            type: "text",
            text: `System prompt untuk ${userName} telah direset ke default BaquaBot.`,
            quoteToken: event.message.quoteToken,
          }
        ]
      });
      return true;
    }

    // Save the new prompt
    await db.set(`customSystemPrompt_${userId}`, newPrompt);
    
    await client.replyMessage({
      replyToken: event.replyToken,
      messages: [
        {
          type: "text",
          text: `System prompt untuk ${userName} berhasil diubah!\n\nUntuk mengeditnya lagi gunakan !sysprompt atau !sysprompt /default untuk kembali ke default.`,
          quoteToken: event.message.quoteToken,
        }
      ]
    });

    return true; // Handled this reply
  }
};

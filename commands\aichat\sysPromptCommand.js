const { QuickDB } = require("quick.db");
const { setSessionTimeout } = require("../../utils/sessionTimeouts");
const db = new QuickDB();

module.exports = {
  command: "sysprompt",
  aliases: ["systemprompt", "prompt"],
  category: "aichat",
  description: "Mengatur system prompt personal untuk chat",
  requiresPrefix: true,
  includes: false,
  handler: async (client, blobClient, event, args) => {
    const userId = event.source.userId;
    const groupId = event.source.groupId || event.source.roomId || userId;

    // Create unique session ID for sysprompt editing
    const sessionId = `${groupId}_${userId}`;

    let user;
    if (event.source.type === "group") {
      user = await client.getGroupMemberProfile(event.source.groupId, event.source.userId);
    } else {
      user = await client.getProfile(event.source.userId);
    }

    const userName = user.displayName;

    // Check if there's already an active sysprompt session for this user
    const isSessionActive = await db.get(`syspromptActive_${sessionId}`);
    if (isSessionActive) {
      return client.replyMessage({
        replyToken: event.replyToken,
        messages: [
          {
            type: "text",
            text: "Kamu sudah memiliki sesi pengeditan system prompt yang aktif. Selesaikan dulu atau tunggu hingga timeout.",
            quoteToken: event.message.quoteToken,
          }
        ]
      });
    }

    // Check if user wants to use default prompt
    if (args.length > 0 && args[0].toLowerCase() === "/default") {
      await db.delete(`customSystemPrompt_${userId}`);
      await client.replyMessage({
        replyToken: event.replyToken,
        messages: [
          {
            type: "text",
            text: `System prompt untuk ${userName} telah direset ke default BaquaBot.`,
            quoteToken: event.message.quoteToken,
          }
        ]
      });
      return;
    }

    // If user provided arguments directly, treat as new prompt
    if (args.length > 0) {
      const newPrompt = args.join(" ");
      await db.set(`customSystemPrompt_${userId}`, newPrompt);

      await client.replyMessage({
        replyToken: event.replyToken,
        messages: [
          {
            type: "text",
            text: `System prompt untuk ${userName} berhasil diubah!\n\nUntuk mengeditnya lagi gunakan !sysprompt atau !sysprompt /default untuk kembali ke default.`,
            quoteToken: event.message.quoteToken,
          }
        ]
      });
      return;
    }

    // If no arguments, show current status and start editing session
    const currentPrompt = await db.get(`customSystemPrompt_${userId}`);

    let statusMessage;
    if (currentPrompt) {
      statusMessage = `${userName}, kamu saat ini menggunakan system prompt personal.\n\nBalas pesan ini dengan prompt baru dalam 10 menit atau ketik /cancel untuk membatalkan.\n\nGunakan /default untuk kembali ke prompt default BaquaBot.`;
    } else {
      statusMessage = `${userName}, kamu saat ini menggunakan system prompt default BaquaBot.\n\nBalas pesan ini dengan prompt personal dalam 10 menit atau ketik /cancel untuk membatalkan.\n\nGunakan /default untuk tetap menggunakan prompt default.`;
    }

    // Activate sysprompt editing session
    await db.set(`syspromptActive_${sessionId}`, true);
    await db.set(`syspromptOwner_${sessionId}`, userId);

    await client.replyMessage({
      replyToken: event.replyToken,
      messages: [
        {
          type: "text",
          text: statusMessage,
          quoteToken: event.message.quoteToken,
        }
      ]
    });

    // Set session timeout (10 minutes)
    setSessionTimeout(sessionId, async () => {
      console.log(`Sysprompt session for ${sessionId} timed out.`);
      await db.delete(`syspromptActive_${sessionId}`);
      await db.delete(`syspromptOwner_${sessionId}`);
    });
  }
};

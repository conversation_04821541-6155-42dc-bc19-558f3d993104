const puppeteer = require("puppeteer-extra");
const StealthPlugin = require("puppeteer-extra-plugin-stealth");
puppeteer.use(StealthPlugin());

module.exports = {
  command: "nekopoi",
  aliases: ["henta<PERSON>oi"],
  category: "anime",
  description: "check upload terbaru nekopoi",
  requiresPrefix: true,
  includes: false,
  handler: async (client, blobClient, event, args) => {
    // client.replyMessage(replyToken, {
    //   type: "text",
    //   text: "Fetching nekopoi...",
    //   quoteToken: event.message.quoteToken
    // });
    try {
      const browser = await puppeteer.launch({
        headless: "new", // You can change this to 'old' for older headless behavior
        args: ["--headless=old"], // Adding the --headless=old flag
      });
      const page = await browser.newPage();
      await page.setUserAgent(
        "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
      );

      const url = "https://nekopoi.care"; // Ganti URL dengan halaman sebenarnya
      await page.goto(url, { waitUntil: "networkidle2" });

      const allCards = await page.evaluate(() => {
        let cards = [];

        // Memilih semua elemen dengan class eropost (card post)
        document.querySelectorAll("#boxid .eropost").forEach((post) => {
          const image = post.querySelector(".eroimg .limitero img")?.src || ""; // Mendapatkan URL gambar
          const title = post.querySelector(".eroinfo h2 a")?.innerText || ""; // Mendapatkan judul
          const link = post.querySelector(".eroinfo h2 a")?.href || ""; // Mendapatkan link
          const date = post.querySelector(".eroinfo span")?.innerText || ""; // Mendapatkan tanggal

          cards.push({ title, link, image, date });
        });
        return cards;
      });

      // Menutup browser setelah selesai
      await browser.close();

      const messageContent = {
        type: "flex",
        altText: "Nekopoi new update",
        contents: {
          type: "carousel",
          contents: allCards.map((card) => ({
            type: "bubble",
            size: "hecto",
            hero: {
              type: "image",
              url: card.image,
              size: "full",
              aspectRatio: "20:13",
              aspectMode: "cover",
            },
            body: {
              type: "box",
              layout: "vertical",
              contents: [
                {
                  type: "text",
                  text: card.title,
                  wrap: true,
                  weight: "bold",
                  size: "md",
                },
                {
                  type: "text",
                  text: card.date,
                  wrap: true,
                },
              ],
            },
            footer: {
              type: "box",
              layout: "vertical",
              spacing: "sm",
              contents: [
                {
                  type: "button",
                  style: "primary",
                  height: "sm",
                  action: {
                    type: "uri",
                    label: "Watch",
                    uri: card.link,
                  },
                  color: "#4DA8DA", // blue
                },
              ],
            },
          })),
        },
      };
      client.replyMessage({
        replyToken: event.replyToken,
        messages: [messageContent],
      });
      // if (event.source.type === "group") {
      //   client.pushMessage(event.source.groupId, messageContent);
      // } else {
      //   client.pushMessage(event.source.userId, messageContent);
      // }
    } catch (error) {
      client.replyMessage({
        replyToken: event.replyToken,
        messages: [
          {
            type: "text",
            text: "Error fetching nekopoi",
            quoteToken: event.message.quoteToken,
          },
        ],
      });
      console.error("Error:", error);
    }
  },
};

const axios = require("axios");

module.exports = {
  command: "enwiki",
  aliases: ["enw"],
  category: "utility",
  description: "<PERSON><PERSON> wiki bahasa inggris",
  requiresPrefix: true,
  includes: false,
  handler: async (client, blobClient, event, args) => {
    try {
      const query = args.join(" ");
      const apiUrl = `https://en.wikipedia.org/api/rest_v1/page/summary/${encodeURIComponent(
        query
      )}`;

      // Mengirim request ke API Wikipedia
      const response = await axios.get(apiUrl);

      // Data dari API
      const summary = response.data;

      // Check if the summary has the necessary information
      if (!summary || !summary.extract || !summary.title) {
        return client.replyMessage({
          replyToken: event.replyToken,
          messages: [
            {
              type: "text",
              text: "❌ Page not found!",
              quoteToken: event.message.quoteToken,
              sender: {
                name: "Wikipedia Summary",
                iconUrl:
                  "https://upload.wikimedia.org/wikipedia/commons/thumb/8/80/Wikipedia-logo-v2.svg/1200px-Wikipedia-logo-v2.svg.png",
              },
            },
          ],
        });
      }

      // If there's an image in the summary
      if (summary.originalimage) {
        client.replyMessage({
          replyToken: event.replyToken,
          messages: [
            {
              type: "image",
              originalContentUrl: summary.originalimage.source,
              previewImageUrl: summary.originalimage.source,
              sender: {
                name: "Wikipedia Summary",
                iconUrl:
                  "https://upload.wikimedia.org/wikipedia/commons/thumb/8/80/Wikipedia-logo-v2.svg/1200px-Wikipedia-logo-v2.svg.png",
              },
            },
            {
              type: "text",
              text: `🌐 ${summary.title}\n\n${summary.extract}\n\n More information: ${summary.content_urls.desktop.page}`,
              quoteToken: event.message.quoteToken,
              sender: {
                name: "Wikipedia Summary",
                iconUrl:
                  "https://upload.wikimedia.org/wikipedia/commons/thumb/8/80/Wikipedia-logo-v2.svg/1200px-Wikipedia-logo-v2.svg.png",
              },
            },
          ],
        });
      } else {
        // If there's no image, just send text
        client.replyMessage({
          replyToken: event.replyToken,
          messages: [
            {
              type: "text",
              text: `🌐 ${summary.title}\n\n${summary.extract}\n\n More Information: ${summary.content_urls.desktop.page}`,
              quoteToken: event.message.quoteToken,
              sender: {
                name: "Wikipedia Summary",
                iconUrl:
                  "https://upload.wikimedia.org/wikipedia/commons/thumb/8/80/Wikipedia-logo-v2.svg/1200px-Wikipedia-logo-v2.svg.png",
              },
            },
          ],
        });
      }
    } catch (error) {
      // Handle any errors (e.g., 404 or other API errors)
      console.error("Error fetching Wikipedia summary:", error.message);

      // Reply with an error message
      return client.replyMessage({
        replyToken: event.replyToken,
        messages: [
          {
            type: "text",
            text: "❌ Page not found!",
            quoteToken: event.message.quoteToken,
            sender: {
              name: "Wikipedia Summary",
              iconUrl:
                "https://upload.wikimedia.org/wikipedia/commons/thumb/8/80/Wikipedia-logo-v2.svg/1200px-Wikipedia-logo-v2.svg.png",
            },
          },
        ],
      });
    }
  },
};

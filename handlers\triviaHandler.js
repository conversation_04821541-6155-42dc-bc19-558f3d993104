const { QuickDB } = require("quick.db");
const db = new QuickDB();
const { fetchTriviaQuestion, formatQuestionPayload, LETTERS } = require('../commands/db/trivia');
const { setSessionTimeout, clearSessionTimeout } = require('../utils/sessionTimeouts');

module.exports = async (client, event) => {
  if (event.message.type !== "text") {
    return false;
  }

  const userId = event.source.userId;
  const groupId = event.source.groupId || event.source.roomId || userId;
  const userMessage = event.message.text.trim().toUpperCase();

  const isGameActive = await db.get(`${groupId}_triviaActive`);
  const gameOwner = await db.get(`${groupId}_triviaOwner`);

  if (!isGameActive || userId !== gameOwner) {
    return false;
  }
  clearSessionTimeout(groupId);
  try {
    let gameState = await db.get(`${groupId}_triviaState`);
    if (!gameState || gameState.stopped || gameState.health <= 0) {
      await db.delete(`${groupId}_triviaActive`);
      await db.delete(`${groupId}_triviaOwner`);
      await db.delete(`${groupId}_triviaState`);
      return false;
    }

    const validAnswers = LETTERS;
    let feedbackText = "";
    let nextQuestionPayload = null;
    let gameContinues = true;

    if (userMessage === "STOP") {
        gameState.stopped = true;
        // Removed markdown from score
        feedbackText = `🛑 Game stopped. Final score: ${gameState.score}. Thanks for playing!`;
        gameContinues = false;
        await db.delete(`${groupId}_triviaActive`);
        await db.delete(`${groupId}_triviaOwner`);
        await db.delete(`${groupId}_triviaState`);

    } else if (validAnswers.includes(userMessage)) {
        const expectedLetter = gameState.correctLetter;
        const expectedAnswerText = gameState.correctAnswerText;

        if (userMessage === expectedLetter) {
            gameState.score++;
            // Removed markdown from answer text
            feedbackText = `✅ Correct! The answer was ${expectedLetter}) ${expectedAnswerText}.`;
        } else {
            gameState.health--;
            // Removed markdown from answer text
            feedbackText = `❌ Incorrect! The correct answer was ${expectedLetter}) ${expectedAnswerText}.\nHealth decreased by 1!`;

            if (gameState.health <= 0) {
                gameContinues = false;
                const finalScore = gameState.score;
                await db.delete(`${groupId}_triviaActive`);
                await db.delete(`${groupId}_triviaOwner`);
                await db.delete(`${groupId}_triviaState`);
                // Removed markdown from score
                feedbackText += `\n\n💀 Out of health! Game over.\nFinal score: ${finalScore}.`;
            }
        }

        if (gameContinues) {
            const nextTriviaData = await fetchTriviaQuestion(gameState.difficulty);
            if (!nextTriviaData || nextTriviaData.error) {
                // Removed markdown from score
                feedbackText += `\n\n⚠️ Failed to fetch next trivia question: ${nextTriviaData?.error || 'Unknown error'}. Game stopped.\nFinal score: ${gameState.score}`;
                gameContinues = false;
                await db.delete(`${groupId}_triviaActive`);
                await db.delete(`${groupId}_triviaOwner`);
                await db.delete(`${groupId}_triviaState`);
            } else {
                nextQuestionPayload = formatQuestionPayload(nextTriviaData, gameState);
                 if (nextQuestionPayload.error) {
                     // Removed markdown from score
                     feedbackText += `\n\n⚠️ Error formatting the next question: ${nextQuestionPayload.error}. Game stopped.\nFinal score: ${gameState.score}`;
                     gameContinues = false;
                     await db.delete(`${groupId}_triviaActive`);
                     await db.delete(`${groupId}_triviaOwner`);
                     await db.delete(`${groupId}_triviaState`);
                 } else {
                    gameState.correctLetter = nextQuestionPayload.correctLetter;
                    gameState.correctAnswerText = nextQuestionPayload.correctAnswerText;
                     await db.set(`${groupId}_triviaState`, gameState);
                 }
            }
        }

    } else {
      return false;
    }

    let finalMessageText = feedbackText;
    if (gameContinues && nextQuestionPayload && nextQuestionPayload.questionText) {
        finalMessageText += `\n\n${nextQuestionPayload.questionText}`;
    } else if (!gameContinues && feedbackText) {
         finalMessageText = feedbackText;
    }

if (finalMessageText) {
        await client.replyMessage({
            replyToken: event.replyToken,
            messages: [{
                type: "text",
                text: finalMessageText.trim(),
            }],
        });

        // If the game continues with a new question, set a new timeout.
        if (gameContinues) {
            setSessionTimeout(groupId, async () => {
                console.log(`Trivia session for group ${groupId} timed out and is being cleaned up.`);
                await db.delete(`${groupId}_triviaActive`);
                await db.delete(`${groupId}_triviaOwner`);
                await db.delete(`${groupId}_triviaState`);
            });
        }
    }

    return true;

  } catch (error) {
    console.error("Error handling Trivia message:", error);
    await db.delete(`${groupId}_triviaActive`);
    await db.delete(`${groupId}_triviaOwner`);
    await db.delete(`${groupId}_triviaState`);
    try {
       await client.replyMessage({
         replyToken: event.replyToken,
         messages: [{ type: "text", text: `An internal error occurred while playing Trivia: ${error.message}. The game has been stopped.` }],
       });
    } catch (replyError) {
      console.error("Failed to send error reply:", replyError);
    }
    return true;
  }
};
const { QuickDB } = require("quick.db");
const db = new QuickDB();
const {
    wordlist,
    getRandomWord,
    getRandomSubstring,
    generatePrompt
} = require('../commands/db/wordguess');
const { setSessionTimeout, clearSessionTimeout } = require('../utils/sessionTimeouts');

module.exports = async (client, event) => {
  if (event.message.type !== "text") {
    return false;
  }

  const userId = event.source.userId;
  const groupId = event.source.groupId || event.source.roomId || userId;
  const userMessage = event.message.text.trim().toLowerCase();

  const isGameActive = await db.get(`${groupId}_wordguessActive`);
  const gameOwner = await db.get(`${groupId}_wordguessOwner`);

  if (!isGameActive || userId !== gameOwner) {
    return false;
  }
clearSessionTimeout(groupId);
  try {
    let gameState = await db.get(`${groupId}_wordguessState`);
    if (!gameState || gameState.stopped || gameState.health <= 0) {
      await db.delete(`${groupId}_wordguessActive`);
      await db.delete(`${groupId}_wordguessOwner`);
      await db.delete(`${groupId}_wordguessState`);
      return false;
    }

    let feedbackText = "";
    let nextPromptText = "";
    let gameContinues = true;
    let isError = false; // Keep track if input is invalid for health deduction

    if (userMessage === "stop") {
        gameState.stopped = true;
        // English, no markdown
        feedbackText = `🛑 Game stopped.\nFinal score: ${gameState.score}. Thanks for playing!`;
        gameContinues = false;
        await db.delete(`${groupId}_wordguessActive`);
        await db.delete(`${groupId}_wordguessOwner`);
        await db.delete(`${groupId}_wordguessState`);

    } else {
        const currentSubstring = gameState.currentSubstring;

        // KEEPING input validation warnings for Wordguess as requested
        if (!wordlist.includes(userMessage)) {
            // English, no markdown
            feedbackText = `⚠️ Word '${userMessage}' not found in the word list.`;
            isError = true;
        } else if (!userMessage.includes(currentSubstring)) {
            // English, no markdown
            feedbackText = `⚠️ Word '${userMessage}' does not contain the snippet '${currentSubstring}'.`;
            isError = true;
        } else if (gameState.usedWords.includes(userMessage)) {
             // English, no markdown
            feedbackText = `❌ Word '${userMessage}' has already been used.`;
            isError = true;
        }

        if (isError) {
            gameState.health--;
            feedbackText += `\nHealth decreased by 1!`; // English

            if (gameState.health <= 0) {
                gameContinues = false;
                const finalScore = gameState.score;
                 await db.delete(`${groupId}_wordguessActive`);
                 await db.delete(`${groupId}_wordguessOwner`);
                 await db.delete(`${groupId}_wordguessState`);
                 // English, no markdown
                feedbackText += `\n\n💀 Out of health! Game over.\nFinal score: ${finalScore}.`;
            } else {
                // Repeat the same prompt if game continues after error
                nextPromptText = generatePrompt(gameState); // Uses updated health
            }
        } else {
            // Correct answer
            gameState.score++;
            gameState.usedWords.push(userMessage);
            feedbackText = `✅ Correct! Score +1.`; // English

            let newWord, newSubstring;
            let attempts = 0;
            do {
                newWord = getRandomWord();
                newSubstring = getRandomSubstring(newWord);
                attempts++;
            } while (!newSubstring && attempts < 10);

            if (!newSubstring) {
                 // English, no markdown
                 feedbackText += `\n\n⚠️ An issue occurred generating a new word snippet. Game stopped.\nFinal score: ${gameState.score}`;
                 gameContinues = false;
                 await db.delete(`${groupId}_wordguessActive`);
                 await db.delete(`${groupId}_wordguessOwner`);
                 await db.delete(`${groupId}_wordguessState`);
            } else {
                gameState.currentSubstring = newSubstring;
                nextPromptText = generatePrompt(gameState); // Generate new prompt
            }
        }
    }

    // Save state ONLY if game continues
    if (gameContinues) {
         await db.set(`${groupId}_wordguessState`, gameState);
    }

    let finalMessage = feedbackText;
    // Append next prompt only if game continues AND there was no error generating it
    if (gameContinues && nextPromptText) {
        finalMessage += `\n\n${nextPromptText}`;
    } else if (!gameContinues && feedbackText) {
        finalMessage = feedbackText; // Only final feedback if game ended
    } else if (isError && gameContinues && nextPromptText) {
         // If error but game continues, show feedback + same prompt
         finalMessage += `\n\n${nextPromptText}`;
    }


    if(finalMessage) {
        await client.replyMessage({
            replyToken: event.replyToken,
            messages: [{ type: "text", text: finalMessage.trim() }],
        });
    }
    if (gameContinues) {
        setSessionTimeout(groupId, async () => {
            console.log(`Word Guess session for group ${groupId} timed out.`);
            await db.delete(`${groupId}_wordguessActive`);
            await db.delete(`${groupId}_wordguessOwner`);
            await db.delete(`${groupId}_wordguessState`);
        });
    }

    // Return true because we handled the message (either valid move or kept warning)
    return true;

  } catch (error) {
    console.error("Error handling Wordguess message:", error);
    await db.delete(`${groupId}_wordguessActive`);
    await db.delete(`${groupId}_wordguessOwner`);
    await db.delete(`${groupId}_wordguessState`);
    try {
       await client.replyMessage({
          replyToken: event.replyToken,
          // English
          messages: [{ type: "text", text: `An internal error occurred while playing Word Guess: ${error.message}. The game has been stopped.` }],
        });
    } catch (replyError) {
      console.error("Failed to send error reply:", replyError);
    }
    return true;
  }
};
module.exports = {
  command: "bookmarked",
  aliases: ["abml"],
  category: "nonprefix",
  description: "<PERSON><PERSON>'s bookmark",
  requiresPrefix: false, // Set to false to enable prefix-less activation
  includes: false, // Set to true if the command should trigger when included in a message
  handler: async (client, blobClient, event, args) => {

    const botInfo = await client.getBotInfo();
    const replyText = "Anime links\n\n"+
    "Hianime : https://hianime.to\n"+
    "Animepahe : https://animepahe.ru\n"+
    "Holodex : https://holodex.net\n"+
    "indexMoe : https://theindex.moe\n\n"+
    

    "Tools links\n\n"+
    "Cobalt video Downloader : https://cobalt.tools\n"+
    "PairDrop file sharing : https://pairdrop.net\n\n"+
    
    "Film\/movie links\n\n"+
    "YTS.MX : https://yts.mx\n"+
    "Watch Cartoon Online : https://www.wco.tv\n\n"+

    `Open source projects powering ${botInfo.displayName} command(s)\n\n`+
    "YT-DLP : https://github.com/yt-dlp/yt-dlp\n"+
    "FFmpeg : https://www.ffmpeg.org\n"+
    "GPT4Free : https://github.com/xtekky/gpt4free\n"+
    "GPT4All : https://github.com/nomic-ai/gpt4all\n"+
    "Jikan : https://jikan.moe\n"+
    "TraceMoe : https://trace.moe\n"+
    "Kokoro TTS : (82M custom param) https://github.com/nazdridoy/kokoro-tts (Not in the bot)"
    

    client.replyMessage({
      replyToken: event.replyToken,
      messages: [
        {
          type: "text",
          text: replyText,
          quoteToken: event.message.quoteToken
        }
      ]
    });
  }
};
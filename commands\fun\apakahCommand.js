module.exports = {
  command: "apakah",
  aliases: ["apa"],
  category: "fun",
  description: "Anda bertanya, bot menjawab",
  requiresPrefix: true,
  includes: false,
  handler: (client, blobClient, event, args) => {
    const answer = [
      "<PERSON><PERSON>",
      "Tidak",
      "Sudah pasti tidak",
      "Ntahlah",
      "Meragukan",
      "Mungkin",
      "BIG <PERSON>",
      "BIG YES",
      "Bentar, coba tanya lagi",
      "Kamu nanya ke bot gitu?",
      "Sangat meragukan",
      "Saya mendukung",
      "Tentu saja",
      "Pasti donk",
      "I Approve",
      "I Disapprove",
      "はい\n\n- YAGOO",
      "Sou desune..",
      "Tentukan sendiri lah",
      "Tidak tau",
      "Coba tanya yang lain",
      "Si",
      "Nein",
      "*nod in agreement",
      "*shake head in disagreement",
      "Kasihtau tama luck lu terlalu jelek sampe dapat option terakhir dari command apakah" // Lowest chance option
    ];

    if (!args[0]) {
      return client.replyMessage({
        replyToken: event.replyToken,
        messages: [
          { type: "text", 
            text: "Apah?", 
            quoteToken: event.message.quoteToken 
          }
        ],
      });
    } else if (
      args[0].toLowerCase() === "apakah?" ||
      args[0].toLowerCase() === "apakaah"
    ) {
      return client.replyMessage({
        replyToken: event.replyToken,
        messages: [
          {
          type: "text",
          text: `Mungkinkah kamu bertanya tentang Alpaka? 😎

👆 Acksually... Alpaka (Bahasa Inggris: Alpaca) 
adalah binatang menyusui dari Amerika Selatan yang menyerupai llama. Alpaka berukuran lebih kecil daripada llama, dan alpaka tak digunakan sebagai hewan pengangkut, tetapi untuk diambil bulunya. Serat alpaka digunakan untuk membuat barang rajutan dan jahitan, seperti wol domba.[1]

Alpaka biasa ditemukan di Argentina bagian utara, dari keluarga Camelidae. Alpaka digembalakan untuk merumput di Pegunungan Andes di Ekuador, Peru selatan, Bolivia utara, dan Cili utara pada ketinggian 3500–5000 m dpl.

sc: https://id.wikipedia.org/wiki/Alpaka,`,
          quoteToken: event.message.quoteToken,
        }
      ],
      });
    } else {
      // Create weighted probabilities where last answer is rare
      const probabilities = Array(answer.length).fill(1); // Default weight = 1
      probabilities[probabilities.length - 1] = 0.01; // Last answer has only 1% normal chance
      const totalWeight = probabilities.reduce((sum, w) => sum + w, 0);

      // Pick an answer based on weighted probability
      let random = Math.random() * totalWeight;
      let selectedIndex = 0;
      for (let i = 0; i < probabilities.length; i++) {
        random -= probabilities[i];
        if (random <= 0) {
          selectedIndex = i;
          break;
        }
      }

      client.replyMessage({
        replyToken: event.replyToken,
        messages: [{ type: "text", 
        text: answer[selectedIndex], 
        quoteToken: event.message.quoteToken }],
      });
    }
  },
};

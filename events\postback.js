module.exports = async (event, client, blobClient) => {
  if (event.postback?.data) {
    const postbackData = event.postback.data;
    const userId = event.source.userId; // Get user ID for cooldown
    let postback = client.postbacks.get(postbackData);

    // Pattern matching for dynamic postbacks
    if (!postback) {
      for (const [key, value] of client.postbacks) {
        if (postbackData.startsWith(key)) {
          postback = value;
          break;
        }
      }
    }

    if (postback?.handler) {
      // Cooldown handling
      if (postback.cooldown) {
        const now = Date.now();
        const cooldownKey = `${postbackData}_${userId}`; // User-specific cooldown
        const lastUsed = client.cooldowns.get(cooldownKey) || 0;
        const remaining = postback.cooldown * 1000 - (now - lastUsed);

        if (remaining > 0) {
          const remainingSeconds = Math.ceil(remaining / 1000);
          return client.replyMessage({
            replyToken: event.replyToken,
            messages: [{
              type: "text",
              text: `Tunggu ${remainingSeconds} detik sebelum menggunakan ini lagi.`
            }]
          });
        }
        client.cooldowns.set(cooldownKey, now);
      }

      // Parameter extraction
      let id;
      if (postback.postbackData && postbackData.startsWith(postback.postbackData)) {
        id = postbackData.split(postback.postbackData)[1];
      }

      try {
        await postback.handler(client, blobClient, event, id);
        console.log(`Handled postback: ${postbackData}`);
      } catch (error) {
        console.error("Postback error:", error);
        client.replyMessage({
          replyToken: event.replyToken,
          messages: [{
            type: "text",
            text: "Terjadi kesalahan saat memproses permintaan."
          }]
        });
      }
    } else {
      client.replyMessage({
        replyToken: event.replyToken,
        messages: [{
          type: "text",
          text: "Fitur ini sedang tidak tersedia."
        }]
      });
    }
  }
};
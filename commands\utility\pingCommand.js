module.exports = {
  command: "ping",
  aliases: ["pong"],
  category: "utility",
  description: "Ordinary ping bot command",
  requiresPrefix: true,
  includes: false,
  handler: async (client, blobClient, event, args) => {
    let profile;
    const start = Date.now();
    // Get user profile based on the event source type
    if (event.source.type === "group") {
      profile = await client.getGroupMemberProfile(
        event.source.groupId,
        event.source.userId
      );
    } else {
      profile = await client.getProfile(event.source.userId);
    }

    // Construct reply text based on source type
    const replyText =
      `Pong! 🏓 Laptop Tama's response time was ${
        Date.now() - start
      }ms\n\n` +
      (event.source.type === "user"
        ? `Wahai ${profile.displayName}, ini bukanlah ping kamu, tapi laptopnya alip.`
        : `Wahai {user}, ini bukanlah ping kamu, tapi laptopnya Tama.`);

    // Send reply message with either substitution or displayName
    client.replyMessage({
      replyToken: event.replyToken,
      messages: [
        {
          type: "textV2",
          text: replyText,
          quoteToken: event.message.quoteToken,
          ...(event.source.type === "group"
            ? {
                substitution: {
                  user: {
                    type: "mention",
                    mentionee: {
                      type: "user",
                      userId: event.source.userId,
                    },
                  },
                },
              }
            : {}),
        },
      ],
    });
  },
};

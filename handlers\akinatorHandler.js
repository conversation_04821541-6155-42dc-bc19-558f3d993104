const { QuickDB } = require("quick.db");
const db = new QuickDB();
const axios = require("axios");
const { setSessionTimeout, clearSessionTimeout } = require("../utils/sessionTimeouts");

module.exports = async (client, event) => {
  const userId = event.source.userId;
  const groupId = event.source.groupId || event.source.roomId;
  const userMessage = event.message.text.toLowerCase();

  try {
    const isAkinatorActive = await db.get(`${groupId}_akinatorActive`);
    const akinatorOwner = await db.get(`${groupId}_akinatorOwner`);

    if (isAkinatorActive && akinatorOwner === userId) {
      clearSessionTimeout(groupId);

      // Handle ending
      if (userMessage === "/end") {
        const sessionId = await db.get(`${groupId}_akinatorSession`);
        if (sessionId) {
          await axios.post("http://localhost:5000/akinator/end", {
            session_id: sessionId,
          });
        }
        await db.delete(`${groupId}_akinatorActive`);
        await db.delete(`${groupId}_akinatorOwner`);
        await db.delete(`${groupId}_akinatorSession`);
        await db.delete(`${groupId}_akinatorStep`);

        return client.replyMessage({
          replyToken: event.replyToken,
          messages: [
            {
              type: "text",
              text: "Akinator game ended in this group. Thanks for playing!",
              quoteToken: event.message.quoteToken,
            },
          ],
        });
      }

      // Parse answer
      let answer = null;
      if (["1", "yes"].includes(userMessage)) answer = "yes";
      else if (["2", "no"].includes(userMessage)) answer = "no";
      else if (["3", "don't know", "dont know"].includes(userMessage)) answer = "idk";
      else if (["4", "probably"].includes(userMessage)) answer = "probably";
      else if (["5", "probably not"].includes(userMessage)) answer = "probably not";
      else {
        return client.replyMessage({
          replyToken: event.replyToken,
          messages: [
            {
              type: "text",
              text: "Please respond with 1 (Yes), 2 (No), 3 (Don't know), 4 (Probably), or 5 (Probably not).",
              quoteToken: event.message.quoteToken,
            },
          ],
        });
      }

      // Continue game via API
      const sessionId = await db.get(`${groupId}_akinatorSession`);
      const res = await axios.post("http://localhost:5000/akinator/answer", {
        session_id: sessionId,
        answer,
      });

      if (res.data.finished && res.data.guess) {
        const guess = res.data.guess;

        // Clean up session
        await db.delete(`${groupId}_akinatorActive`);
        await db.delete(`${groupId}_akinatorOwner`);
        await db.delete(`${groupId}_akinatorSession`);
        await db.delete(`${groupId}_akinatorStep`);

        return client.replyMessage({
          replyToken: event.replyToken,
          messages: [
            {
              type: "text",
              text: `I think your character is: ${guess.name_proposition}\nDescription: ${guess.description_proposition}`,
              quoteToken: event.message.quoteToken,
            },
            {
              type: "image",
              originalContentUrl: guess.photo,
              previewImageUrl: guess.photo,
            },
          ],
        });
      } else {
        const currentStep = res.data.step;
        await db.set(`${groupId}_akinatorStep`, currentStep);

        client.replyMessage({
          replyToken: event.replyToken,
          messages: [
            {
              type: "text",
              text: `Question ${currentStep + 1}: ${res.data.question}\nAnswers:\n1. Yes\n2. No\n3. Don't know\n4. Probably\n5. Probably not\n\n... or type "/end" to end the game`,
              quoteToken: event.message.quoteToken,
            },
          ],
        });
      }

      setSessionTimeout(groupId, async () => {
        await db.delete(`${groupId}_akinatorActive`);
        await db.delete(`${groupId}_akinatorOwner`);
        await db.delete(`${groupId}_akinatorSession`);
        await db.delete(`${groupId}_akinatorStep`);
      });
    }

    return false; // Non-owner messages are ignored
  } catch (error) {
    await db.delete(`${groupId}_akinatorActive`);
    await db.delete(`${groupId}_akinatorOwner`);
    await db.delete(`${groupId}_akinatorSession`);
    await db.delete(`${groupId}_akinatorStep`);

     client.replyMessage({
      replyToken: event.replyToken,
      messages: [
        {
          type: "text",
          text: `An error occurred, and the game has been stopped. Error code: ${error.message}`,
          quoteToken: event.message.quoteToken,
        },
      ],
    });
    console.error("Error handling Akinator message:", error);
  }
};

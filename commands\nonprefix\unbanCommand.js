module.exports = {
  command: "unban",
  aliases: [""],
  category: "nonprefix",
  description: "Unban a user",
  requiresPrefix: true, // Set to false to enable prefix-less activation
  includes: false, // Set to true if the command should trigger when included in a message
  handler: async (client, blobClient, event, args) => {
    
    if (event.source.userId !== process.env.tama) {
      return client.replyMessage({
        replyToken: event.replyToken,
        messages: [
          {
            type: "text",
            text: "Nee.. baka nano? you can't unban User\n\nNever thought a normal user would accidentally found this command.",
            quoteToken: event.message.quoteToken,
          },
        ],
      });
    }
    if (event.source.type !== "group") {
      return client.replyMessage({
        replyToken: event.replyToken,
        messages: [
          {
            type: "text",
            text: "This command can only be used in groups!",
            quoteToken: event.message.quoteToken,
          },
        ],
      });
    }
    if (!args.length) {
      return client.replyMessage({
        replyToken: event.replyToken,
        messages: [
          {
            type: "text",
            text: "Please provide a user ID to unban.",
            quoteToken: event.message.quoteToken,
          },
        ],
      });
    }
    client.replyMessage({
      replyToken: event.replyToken,
      messages: [
        {
          type: "text",
          text: "User has been unbanned. Think before you act next time.",
          quoteToken: event.message.quoteToken,
        }
      ]
    });
  }
};
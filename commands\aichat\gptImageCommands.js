const axios = require("axios");

module.exports = {
  command: "gpti",
  aliases: ["generateimage", "gimage"],
  category: "aichat",
  description: "Menggunakan G4F untuk membuat gambar berdasarkan prompt",
  requiresPrefix: true,
  includes: false,
  handler: async (client, blobClient, event, args) => {
    if (!args.length) {
      await client.replyMessage({
        replyToken: event.replyToken,
        messages: [
          {
            type: "text",
            text: "Kirimkan prompt untuk membuat gambar: !image <prompt>",
          },
        ],
      });
      return;
    }

    const prompt = args.join(" ");

    try {
      // Kirim permintaan ke Python API
      const response = await axios.post("http://localhost:5000/image", {
        prompt: prompt,
      });

      const imageUrl = response.data.url;

      // Kirim gambar ke pengguna
      await client.replyMessage({
        replyToken: event.replyToken,
        messages: [
          {
            type: "image",
            originalContentUrl: imageUrl,
            previewImageUrl: imageUrl,
          },
        ],
      });
    } catch (error) {
      console.error("Error generating image:", error.message);
      await client.replyMessage({
        replyToken: event.replyToken,
        messages: [
          {
            type: "text",
            text: "Terjadi kesalahan saat membuat gambar. Coba lagi nanti.",
          },
        ],
      });
    }
  },
};

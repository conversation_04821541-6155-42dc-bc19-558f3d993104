const ffmpeg = require("fluent-ffmpeg");

// **Audio filter presets**
const audioFilterMap = {
  bassboost: "equalizer=f=50:width_type=h:width=300:g=20",
  karaoke: "stereotools=mlev=0.03",
  trebleboost: "equalizer=f=3000:width_type=h:width=200:g=5",
  pitch: "asetrate=44100*1.2,aresample=44100",
  tempo: "atempo=1.5",
  slowtempo: "atempo=0.50",
  nightcore: "asetrate=44100*1.25,aresample=44100,atempo=1.25",
  vaporwave: "asetrate=44100*0.8,aresample=44100",
  reverb: "aecho=1.0:1.0:2000:0.8",
  flanger: "flanger",
  echo: "aecho=1.0:1.0:100:0.8",
  surround: "surround",
  phaser: "aphaser=in_gain=0.4",
  reverse: "areverse",
  distortion: "acrusher=bits=2:mix=1:mode=log:level_in=2.0:level_out=2.0",
  chipmunk: "asetrate=44100*1.5,aresample=44100",
  liverquad: "apulsator=hz=1",
  chorus: "chorus=0.5:0.9:50:0.4:0.25:2",
  lowpass: "lowpass=f=1000:width=200",
  stutter: "aloop=loop=1:size=44100",
  eq: "firequalizer=gain='if(lt(f,1000),20,0)'",
  destroy: "acrusher=bits=8:mix=0.5",
  earwax: "earwax",
  phaserfx: "aphaser=in_gain=0.4:out_gain=0.74:delay=3:decay=0.4:speed=0.5",
  softdist: "acrusher=level_in=2:bits=8:mode=log",
  lofi: "lowpass=f=1000,highpass=f=150,asetrate=32000,aresample=44100,acompressor=threshold=-20dB:ratio=4,afade=in:st=0:d=2",
  dying: "equalizer=f=100:t=h:width=200:g=6, equalizer=f=3000:t=h:width=200:g=4, aecho=0.8:0.9:100:0.3, chorus=0.5:0.7:50:0.4:0.3:0.5, flanger, bass=g=12, treble=g=8, stereotools=mlev=2, atempo=1.05, extrastereo=m=2, lowpass=f=3500, highpass=f=200, vibrato=f=6.5, tremolo=f=5:d=0.8"
};

// **Video filter presets - Mobile-compatible fun visual effects**
const videoFilterMap = {
  // Color effects (mobile-safe)
  vintage: "colorchannelmixer=.393:.769:.189:0:.349:.686:.168:0:.272:.534:.131,curves=increase_contrast",
  sepia: "colorchannelmixer=.393:.769:.189:0:.349:.686:.168:0:.272:.534:.131",
  blackwhite: "hue=s=0",
  negative: "negate",
  warm: "eq=temperature=3000:saturation=1.1",
  cool: "eq=temperature=7000:saturation=1.1",

  // Visual distortions (simplified for compatibility)
  blur: "boxblur=3:1",
  sharpen: "unsharp=5:5:0.8:5:5:0.0",
  pixelate: "scale=iw/8:ih/8:flags=neighbor,scale=iw*8:ih*8:flags=neighbor",
  mirror: "crop=iw/2:ih:0:0,split[left][tmp];[tmp]hflip[right];[left][right]hstack",

  // Speed effects (safe)
  slowmo: "setpts=2.0*PTS", // 0.5x speed
  fastmo: "setpts=0.5*PTS", // 2x speed
  timelapse: "setpts=0.25*PTS", // 4x speed

  // Artistic effects (simplified)
  cartoon: "edgedetect=low=0.1:high=0.4",
  emboss: "convolution='0 -1 0 -1 4 -1 0 -1 0:0 -1 0 -1 4 -1 0 -1 0:0 -1 0 -1 4 -1 0 -1 0:0 -1 0 -1 4 -1 0 -1 0:1:1:1:1:0:128:128:128'",

  // Simple color effects
  bright: "eq=brightness=0.3",
  dark: "eq=brightness=-0.3",
  contrast: "eq=contrast=1.5",
  saturate: "eq=saturation=2.0",

  // Safe glitch effects
  noise: "noise=alls=10:allf=t",
  rgb: "split=3[r][g][b];[r]lutrgb=g=0:b=0[r];[g]lutrgb=r=0:b=0[g];[b]lutrgb=r=0:g=0[b];[r][g]blend=all_mode=screen[rg];[rg][b]blend=all_mode=screen",

  // Simple fun effects
  hueshift: "hue=h=90",
  rainbow: "hue=h=2*PI*t*30",

  // Retro effects (simplified)
  vhs: "noise=alls=5:allf=t,eq=saturation=0.8",
  crt: "scale=320:240,scale=iw*2:ih*2:flags=neighbor",
  gameboy: "scale=160:144,scale=iw*4:ih*4:flags=neighbor,colorchannelmixer=.3:.4:.3:0:.3:.4:.3:0:.3:.4:.3",

  // Simple transition effects
  fade: "fade=in:0:15",

  // Rotation effects (limited)
  flip: "hflip",
  flop: "vflip",
  rotate90: "transpose=1",
  rotate180: "transpose=2,transpose=2",
  rotate270: "transpose=2"
};

/**
 * **Apply audio filters to audio files using FFmpeg**
 * @param {string} inputPath - Path to input file (original file)
 * @param {string} outputPath - Path to output file (filtered file)
 * @param {string[]} filters - Array of filters to apply
 * @returns {Promise<void>}
 */
const applyAudioFilters = (inputPath, outputPath, filters) => {
  return new Promise((resolve, reject) => {
    const command = ffmpeg(inputPath);

    // Convert preset and custom filters to FFmpeg filter strings
    const ffmpegFilters = filters.map((filter) => audioFilterMap[filter] || filter).filter(Boolean);

    if (ffmpegFilters.length > 0) {
      command.audioFilters(ffmpegFilters);
    }

    // Add mobile-compatible audio encoding settings
    command
      .audioCodec('libmp3lame')        // Use LAME MP3 encoder for best compatibility
      .format('mp3')                   // Ensure MP3 format
      .outputOptions([
        '-preset fast',                // Fast encoding preset
        '-b:a 192k',                   // Audio bitrate 192kbps (good quality)
        '-ar 44100',                   // Standard sample rate
        '-ac 2',                       // Stereo audio
        '-joint_stereo 1',             // Enable joint stereo for better compression
        '-compression_level 2',        // Fast compression
        '-reservoir 0',                // Disable bit reservoir for faster encoding
        '-write_xing 0'                // Disable Xing header for faster processing
      ])
      .save(outputPath)
      .on("end", resolve)
      .on("error", reject);
  });
};

/**
 * **Apply video filters to video files using FFmpeg**
 * @param {string} inputPath - Path to input file (original file)
 * @param {string} outputPath - Path to output file (filtered file)
 * @param {string[]} filters - Array of filters to apply
 * @returns {Promise<void>}
 */
const applyVideoFilters = (inputPath, outputPath, filters) => {
  return new Promise((resolve, reject) => {
    const command = ffmpeg(inputPath);

    // Convert preset and custom filters to FFmpeg filter strings
    const ffmpegFilters = filters.map((filter) => videoFilterMap[filter] || filter).filter(Boolean);

    if (ffmpegFilters.length > 0) {
      command.videoFilters(ffmpegFilters);
    }

    // Add mobile-compatible encoding settings
    command
      .videoCodec('libx264')           // Use H.264 codec for maximum compatibility
      .audioCodec('aac')               // Use AAC audio codec
      .format('mp4')                   // Ensure MP4 container format
      .outputOptions([
        '-preset fast',                // Fast encoding preset
        '-crf 23',                     // Good quality/size balance
        '-movflags +faststart',        // Enable progressive download
        '-pix_fmt yuv420p',           // Pixel format compatible with most devices
        '-profile:v baseline',         // H.264 baseline profile for maximum compatibility
        '-level 3.0',                  // H.264 level 3.0 for mobile compatibility
        '-r 30',                       // Set frame rate to 30fps
        '-g 60',                       // Set keyframe interval
        '-sc_threshold 0',             // Disable scene change detection
        '-b:v 1000k',                  // Set video bitrate to 1Mbps
        '-maxrate 1500k',              // Maximum bitrate
        '-bufsize 2000k',              // Buffer size
        '-b:a 128k',                   // Audio bitrate 128kbps
        '-ar 44100',                   // Audio sample rate
        '-ac 2'                        // Stereo audio
      ])
      .save(outputPath)
      .on("end", resolve)
      .on("error", reject);
  });
};

// Legacy function for backward compatibility
const applyFilters = applyAudioFilters;

module.exports = {
  applyFilters,
  applyAudioFilters,
  applyVideoFilters,
  filterMap: audioFilterMap, // Legacy export
  audioFilterMap,
  videoFilterMap
};

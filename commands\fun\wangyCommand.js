module.exports = {
  command: "wangy",
  aliases: [],
  category: "fun",
  description: "wangy command",
  requiresPrefix: true,
  includes: false,
  handler: (client, blobClient, event, args) => {
    const wangy = args[0].toUpperCase();
    let wangi = `${wangy}...... ${wangy} ❤️ ❤️ ❤️ WANGY WANGY WANGY WANGY WANGY WANGY HU HA HU HA HU HA, aaaah baunya ${wangy} wangi aku mau nyiumin aroma wanginya ${wangy} AAAAAAAAH ~~ Rambutnya.... aaah rambutnya juga pengen aku elus-elus ~ AAAAAH ${wangy} ❤️ ❤️ ❤️   manis banget AAAAAAAAH TATAPAN ${wangy} BEGITU MENGGODAAAAAAAAA............ GUA RELA JADI BUDAK SIMP HANYA DEMI ${wangy} TERDJINTA AAAAAAAAAAAAAAAAAAAAAAAAAAAAAGH apa ? ${wangy} itu gak nyata ? Cuma karakter 2 dimensi katamu ? nggak, ngak ngak ngak ngak NGAAAAAAAAK GUA GAK PERCAYA ITU DIA NYATA !! GUA GAK PEDULI SAMA KENYATAAN POKOKNYA GAK PEDULI. ${wangy} ngeliat gw ... ${wangy} NGELIATIN GW! ${wangy}... kamu percaya sama aku ? aaaaaaaaaaah syukur ${wangy} gak malu memiliki aku aaaaaah ❤️ ❤️ ❤️ YEAAAAAAAAAAAH GUA MASIH PUNYA ${wangy}, ${wangy} AKU SAYANG ${wangy} AKU CINTA ${wangy} AKU AKU INGIN ${wangy} MENJADI BIDADARIKUUUUUUU!!!!!!!!!!!!! AAAAAAAAAAAAAAAAAAAAAAAAAAAAAGH!!!!!!!!!!!!!!!!!!!!!!`;
    client.replyMessage({
      replyToken: event.replyToken,
      messages: [
        {
          type: "text",
          text: wangi,
          quoteToken: event.message.quoteToken,
        },
      ],
    });
  },
};

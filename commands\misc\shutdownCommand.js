const { exec } = require("child_process");

module.exports = {
  command: "shutdown",
  aliases: ["stdn", "sd", "poweroff"],
  category: "misc",
  description: "Shut down the bot",
  requiresPrefix: true,
  includes: false,
  handler: async (client, blobClient, event, args) => {
    const replyToken = event.replyToken;

    try {
      // Periksa apakah pengguna memiliki izin
      if (event.source.userId !== process.env.tama) {
        return client.replyMessage({
          replyToken,
          messages: [
            {
              type: "text",
              text: "You don't have the power to shut me down!",
              quoteToken: event.message.quoteToken,
            },
          ],
        });
      }

      // Kirim pesan konfirmasi kepada pengguna sebelum shutdown
      await client.replyMessage({
        replyToken,
        messages: [
          {
            type: "text",
            text: "Shutting down... Goodbye!",
            quoteToken: event.message.quoteToken,
          },
        ],
      });

      // Platform-specific shutdown command
      const shutdownCommand = process.platform === "win32"
        ? 'taskkill /f /im node.exe'
        : `kill -9 ${process.pid}`;

      // Eksekusi perintah untuk menghentikan proses Node.js
      exec(shutdownCommand, (error, stdout, stderr) => {
        if (error) {
          console.error(`Error during shutdown: ${error.message}`);
          return client.replyMessage({
            replyToken,
            messages: [
              {
                type: "text",
                text: "Failed to shut down the bot. Please try again later.",
                quoteToken: event.message.quoteToken,
              },
            ],
          });
        }
        if (stderr) {
          console.error(`Stderr: ${stderr}`);
        }
        console.log(`Stdout: ${stdout}`);
        process.exit(0); // Pastikan proses utama juga berhenti
      });
    } catch (err) {
      console.error(`Failed to shut down the bot: ${err.message}`);
    }
  },
};

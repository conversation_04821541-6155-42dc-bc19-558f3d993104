module.exports = {
  /**
   * Converts a duration in seconds to a formatted time string hh:mm:ss or mm:ss
   * @param {number} duration - Duration in seconds to be converted.
   * @returns {string} - Time formatted as hh:mm:ss (if hours exist) or mm:ss.
   * 
   * @example
   * const duration = 3720; // 1 hour 2 minutes 0 seconds
   * console.log(formatDuration(duration)); // Output: '01:02:00'
   * 
   * @example
   * const duration = 120; // 2 minutes 0 seconds
   * console.log(formatDuration(duration)); // Output: '02:00'
   */
  formatDuration: (duration) => {
    let hours = Math.floor(duration / 3600); // Calculate hours
    let minutes = Math.floor((duration % 3600) / 60); // Calculate minutes
    let seconds = (duration % 60).toFixed(0) // Calculate seconds

    return (
      (hours > 0 ? String(hours).padStart(2, '0') + ':' : '') + // Add hours if available
      String(minutes).padStart(2, '0') + ':' + // Format minutes with 2 digits
      String(seconds).padStart(2, '0') // Format seconds with 2 digits
    );
  },
/**
 * Downloads a photo by triggering the provided callback.
 * 
 * @param {string} url - The URL of the image to download.
 * @param {string} name - The name to give the downloaded image.
 * @param {number} retries - The number of retry attempts in case of failure.
 * @param {function} callback - A callback function to handle the photo download. 
 *        The callback will be called with the parameters (url, name, type, retries).
 */
 download_photo(url, name, retries, callback) {
  const type = "jpg"; // Change this if you want to support other file types
  callback(url, name, type, retries);
},

/**
 * Pauses the execution for a specified amount of time.
 * 
 * @param {number} ms - The amount of time to sleep, in milliseconds.
 * @returns {Promise} A promise that resolves after the specified duration.
 */
 sleep(ms) {
  return new Promise((resolve) => setTimeout(resolve, ms));
},


  /**
   * Converts a duration in milliseconds to a formatted time string hh:mm:ss
   * @param {number} duration - Duration in milliseconds to be converted.
   * @returns {string} - Time formatted as hh:mm:ss (hours included if non-zero).
   * 
   * @example
   * const duration = 3720000; // 1 hour 2 minutes 0 seconds
   * console.log(formatMilliseconds(duration)); // Output: '01:02:00'
   * 
   * @example
   * const duration = 120000; // 2 minutes 0 seconds
   * console.log(formatMilliseconds(duration)); // Output: '02:00'
   */
  formatMilliseconds: (duration) => {
    let hours = Math.floor(duration / 3600000); // Calculate hours
    let minutes = Math.floor((duration % 3600000) / 60000); // Calculate minutes
    let seconds = Math.floor((duration % 60000) / 1000).toFixed(0); // Calculate seconds

    return (
      (hours > 0 ? String(hours).padStart(2, '0') + ':' : '') + // Add hours if available
      String(minutes).padStart(2, '0') + ':' + // Format minutes with 2 digits
      String(seconds).padStart(2, '0') // Format seconds with 2 digits
    );
  },
/**
   * Truncates a string to a specified length and adds an ellipsis (...) if the string exceeds that length.
   * @param {string} text - The string to be truncated.
   * @param {number} maxLength - The maximum allowed length of the string before truncating.
   * @returns {string} - The truncated string with an ellipsis added if necessary.
   * 
   * @example
   * const text = "This is a very long text that needs to be truncated.";
   * console.log(TruncateText(text, 20)); // Output: 'This is a very long...'
   * 
   * @example
   * const text = "Short text";
   * console.log(TruncateText(text, 20)); // Output: 'Short text' (no truncation)
   */
  TruncateText: (text, maxLength) => {
    if (text.length > maxLength) {
      return text.slice(0, maxLength) + '...';
    }
    return text;
    
  }
  
};  


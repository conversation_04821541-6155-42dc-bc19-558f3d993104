const express = require("express");
const path = require("path");
const crypto = require("crypto");
const fs = require("fs");

// Rate limiting store (in production, use Redis or similar)
const rateLimitStore = new Map();

// Clean up rate limit store every 5 minutes
setInterval(() => {
  const now = Date.now();
  for (const [key, data] of rateLimitStore.entries()) {
    if (now - data.resetTime > 300000) { // 5 minutes
      rateLimitStore.delete(key);
    }
  }
}, 300000);

/**
 * Security middleware for static files
 */
const secureStaticMiddleware = (req, res, next) => {
  const clientIP = req.ip || req.connection.remoteAddress;
  const userAgent = req.get("User-Agent") || "";
  
  // Rate limiting per IP
  const rateLimitKey = clientIP;
  const now = Date.now();
  const windowMs = 60000; // 1 minute
  const maxRequests = 30; // 30 requests per minute per IP
  
  if (!rateLimitStore.has(rateLimitKey)) {
    rateLimitStore.set(rateLimitKey, {
      count: 1,
      resetTime: now + windowMs
    });
  } else {
    const rateData = rateLimitStore.get(rateLimitKey);
    if (now > rateData.resetTime) {
      rateData.count = 1;
      rateData.resetTime = now + windowMs;
    } else {
      rateData.count++;
      if (rateData.count > maxRequests) {
        return res.status(429).json({ error: "Too many requests" });
      }
    }
  }

  // Normalize path to remove double slashes
  const normalizedPath = req.path.replace(/\/+/g, '/');

  // Check for signed URLs (for sensitive content)
  const { token, expires, signature } = req.query;
  if (token && expires && signature) {
    if (!verifySignedUrl(normalizedPath, token, expires, signature)) {
      return res.status(403).send(create403Page());
    }
  }

  // Check if this is a genuine LINE client
  const isLineClient = /^Line\/\d+\.\d+\.\d+/.test(userAgent); // Matches "Line/15.7.1" pattern

  // Check if this is sensitive content (downloads, images, etc.)
  const isSensitiveContent = normalizedPath.includes('/downloads/') ||
                            normalizedPath.match(/\.(jpg|jpeg|png|gif|mp4|mp3|avi|mov|wav|pdf)$/i);

  // For sensitive content, ONLY allow LINE clients with proper user agent
  if (isSensitiveContent) {
    if (!isLineClient && (!token || !signature)) {
      console.log(`Blocked non-LINE access to sensitive content: ${normalizedPath} from ${clientIP} (${userAgent})`);
      return res.status(403).send(create403Page());
    }

    // Log LINE client access
    if (isLineClient) {
      console.log(`LINE client access: ${normalizedPath} from ${clientIP} (${userAgent})`);
    }
  }

  // Log access for monitoring
  console.log(`Static file access: ${normalizedPath} from ${clientIP} (${userAgent.substring(0, 100)})`);

  // Serve the static file
  const staticPath = path.join(__dirname, "../static");
  const filePath = path.join(staticPath, normalizedPath);

  // Security check: prevent directory traversal
  if (!filePath.startsWith(staticPath)) {
    return res.status(403).send(create403Page());
  }

  // Check if file exists
  if (!fs.existsSync(filePath)) {
    return res.status(404).send(`
<!DOCTYPE html>
<html>
<head><title>404 - File Not Found</title></head>
<body style="font-family: Arial, sans-serif; text-align: center; padding: 50px;">
<h1>404 - File Not Found</h1>
<p>The requested file could not be found.</p>
</body>
</html>`);
  }

  // Set security headers
  res.set({
    'X-Content-Type-Options': 'nosniff',
    'X-Frame-Options': 'DENY',
    'X-XSS-Protection': '1; mode=block',
    'Cache-Control': 'private, max-age=3600', // 1 hour cache
    'Referrer-Policy': 'strict-origin-when-cross-origin'
  });

  // Serve the file
  res.sendFile(filePath);
};

/**
 * Verify signed URL
 */
function verifySignedUrl(path, token, expires, signature) {
  try {
    // Check if token has expired
    const expiresTime = parseInt(expires);
    if (Date.now() > expiresTime) {
      return false;
    }

    // Verify signature
    const secret = process.env.channelSecret || 'default-secret';
    const expectedSignature = crypto
      .createHmac('sha256', secret)
      .update(`${path}:${token}:${expires}`)
      .digest('hex');

    return crypto.timingSafeEqual(
      Buffer.from(signature, 'hex'),
      Buffer.from(expectedSignature, 'hex')
    );
  } catch (error) {
    console.error('Error verifying signed URL:', error);
    return false;
  }
}

/**
 * Create a 403 Forbidden HTML page
 */
function create403Page() {
  return `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>403 - Access Forbidden</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            margin: 0;
            padding: 0;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .container {
            background: white;
            padding: 3rem;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            text-align: center;
            max-width: 500px;
            margin: 2rem;
        }
        .error-code {
            font-size: 6rem;
            font-weight: bold;
            color: #e74c3c;
            margin: 0;
            line-height: 1;
        }
        .error-title {
            font-size: 2rem;
            color: #2c3e50;
            margin: 1rem 0;
        }
        .error-message {
            color: #7f8c8d;
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 2rem;
        }
        .shield-icon {
            font-size: 4rem;
            margin-bottom: 1rem;
        }
        .back-link {
            display: inline-block;
            background: #3498db;
            color: white;
            padding: 12px 24px;
            text-decoration: none;
            border-radius: 6px;
            transition: background 0.3s;
        }
        .back-link:hover {
            background: #2980b9;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="shield-icon">🛡️</div>
        <h1 class="error-code">403</h1>
        <h2 class="error-title">Access Forbidden</h2>
        <p class="error-message">
            Kamu mau ngapain hayo, gabole
        </p>
        <a href="javascript:history.back()" class="back-link">← Go Back</a>
    </div>
</body>
</html>`;
}

/**
 * API middleware for external services (TraceMoe, SauceNao, etc.)
 * Requires a valid API token or must be from localhost
 */
const apiStaticMiddleware = (req, res, next) => {
  const clientIP = req.ip || req.connection.remoteAddress;
  const userAgent = req.get("User-Agent") || "";

  // Check for API token in query parameters or headers
  const apiToken = req.query.api_token || req.get('X-API-Token');
  const validApiToken = process.env.API_TOKEN || 'default-api-token';

  // Require valid API token for ALL requests (no localhost exception)
  if (apiToken !== validApiToken) {
    console.log(`Blocked unauthorized API access from: ${clientIP} (${userAgent}) - Invalid or missing API token`);
    return res.status(403).send(create403Page());
  }

  // Rate limiting per IP (more restrictive for API access)
  const rateLimitKey = `api_${clientIP}`;
  const now = Date.now();
  const windowMs = 60000; // 1 minute
  const maxRequests = 10; // 10 requests per minute per IP for API access

  if (!rateLimitStore.has(rateLimitKey)) {
    rateLimitStore.set(rateLimitKey, {
      count: 1,
      resetTime: now + windowMs
    });
  } else {
    const rateData = rateLimitStore.get(rateLimitKey);
    if (now > rateData.resetTime) {
      rateData.count = 1;
      rateData.resetTime = now + windowMs;
    } else {
      rateData.count++;
      if (rateData.count > maxRequests) {
        return res.status(429).json({ error: "Too many requests" });
      }
    }
  }

  // Normalize path to remove double slashes and /api/static prefix
  const normalizedPath = req.path.replace(/\/+/g, '/').replace('/api/static', '');

  // Log API access for monitoring
  console.log(`API access: ${normalizedPath} from ${clientIP} (${userAgent.substring(0, 100)})`);

  // Serve the static file
  const staticPath = path.join(__dirname, "../static");
  const filePath = path.join(staticPath, normalizedPath);

  // Security check: prevent directory traversal
  if (!filePath.startsWith(staticPath)) {
    return res.status(403).json({ error: "Access denied" });
  }

  // Check if file exists
  if (!fs.existsSync(filePath)) {
    return res.status(404).json({ error: "File not found" });
  }

  // Set security headers for API access
  res.set({
    'X-Content-Type-Options': 'nosniff',
    'Cache-Control': 'public, max-age=300', // 5 minutes cache for API
    'Access-Control-Allow-Origin': '*', // Allow CORS for API access
  });

  // Serve the file
  res.sendFile(filePath);
};

module.exports = {
  secureStaticMiddleware,
  apiStaticMiddleware
};

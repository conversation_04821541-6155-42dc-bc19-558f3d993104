module.exports = async (event, client, blobClient) => {
  // Loop through all new members
  const groupSummary = await client.getGroupSummary(event.source.groupId);
  const tamaChan = await client.getProfile(process.env.tama);

  // <PERSON><PERSON> pesan selamat datang untuk setiap anggota yang baru bergabung
  for (const member of event.joined.members) {
    const newMember = await client.getGroupMemberProfile(
      event.source.groupId,
      member.userId
    );
    const introText = 
    "\n\n🌟 Introduction 🌟\n\n" +
    "Saya adalah bot untuk grup ini! Untuk melihat daftar perintah yang tersedia, ketik !help atau mention saya.\n\n" +
    "📌 Links 📌\n" +
    "🔹 Discord Server Garde X SiRiUS : Rising - https://discord.gg/YvP9fK2 \n" +
    "🔹 Spreadsheet Member SiRiUS - https://docs.google.com/spreadsheets/d/10hRLOkATsO93xBwB1zktPG_wSN9Ney8ejO_sYwP8rqg";
    
    const welcomeMessage = {
      type: "text",
      text:
        event.source.groupId === process.env.sirius
          ? `Halo ${newMember.displayName}! Selamat datang di ${groupSummary.groupName}, Mention ${tamaChan.displayName} untuk di scout yak` + introText
          : `Halo ${newMember.displayName}! Selamat datang di ${groupSummary.groupName} 
=============
Introduction :
=============

Saya adalah bot untuk GC ini, untuk melihat commands apa saja yang bisa kamu gunakan ketik !help atau mention saya!`,
    };

    // Reply the welcome message
    await client.replyMessage({
      replyToken: event.replyToken,
      messages: [welcomeMessage],
    })
  }
};

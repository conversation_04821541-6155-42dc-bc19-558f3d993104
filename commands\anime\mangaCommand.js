const axios = require("axios");

module.exports = {
  command: "manga",
  aliases: [],
  category: "anime",
  description: "Cari info manga/novel/manhwa/one-shot",
  requiresPrefix: true,
  includes: false,
  handler: async (client, blobClient, event, args) => {
    const query = args.join(" ");
    if (!query)
      return client.replyMessage({
        replyToken: event.replyToken,
        messages: [
          {
            type: "text",
            text: "Please input manga name",
            quoteToken: event.message.quoteToken,
          },
        ],
      });
    const getMangaSearch = await axios(
      `https://api.jikan.moe/v4/manga?q=${encodeURIComponent(
        query
      )}&limit=12&page=1`
    );
    if (getMangaSearch.data.data.length === 0 || !getMangaSearch)
      return client.replyMessage({
        replyToken: event.replyToken,
        messages: [
          {
            type: "text",
            text: "Manga not found",
            quoteToken: event.message.quoteToken,
          },
        ],
      });
    const messageContent = {
      type: "flex",
      altText: "MAL search result",
      contents: {
        type: "carousel",
        contents: getMangaSearch.data.data.map((manga) => ({
          type: "bubble",
          size: "hecto",
          hero: {
            type: "image",
            url: manga.images.jpg.large_image_url,
            size: "full",
            aspectRatio: "2:3",
            aspectMode: "cover",
          },
          body: {
            type: "box",
            layout: "vertical",
            spacing: "sm",
            contents: [
              {
                type: "text",
                text: manga.title,
                wrap: true,
                weight: "bold",
                size: "sm",
              },
              {
                type: "box",
                layout: "baseline",
                contents: [
                  {
                    type: "text",
                    text: manga.type,
                    wrap: true,
                    size: "sm",
                    color: "#999999",
                  },
                ],
              },
            ],
          },
          footer: {
            type: "box",
            layout: "vertical",
            spacing: "sm",
            contents: [
              {
                type: "button",
                style: "primary",
                height: "sm",
                action: {
                  type: "uri",
                  label: "MyAnimeList",
                  uri: `https://myanimelist.net/manga/${manga.mal_id}`,
                },
                color: "#4DA8DA", // blue
              },
              {
                type: "button",
                style: "primary",
                height: "sm",
                action: {
                  type: "postback",
                  label: "See Details",
                  data: `mangaData=${manga.mal_id}`,
                  displayText: `See Details ${manga.title}`,
                },
                color: "#FF6B9D", // pink
              },
            ],
          },
        })),
      },
    };

    client.replyMessage({
      replyToken: event.replyToken,
      messages: [messageContent],
    });
  },
};

const { spawn } = require("child_process");
const fs = require("fs");
const path = require("path");
const { formatDuration } = require("../../utils/utils");
const { generateSecureUrl } = require("../../utils/urlSigner");

module.exports = {
  command: "mp4",
  aliases: ["ytv"],
  category: "utility",
  description: "Download video from links",
  requiresPrefix: true,
  includes: false,
  handler: async (client, blobClient, event, args) => {
    const videoUrl = event.message.text.split(" ")[1]; 
    const maxFileSize = 200 * 1024 * 1024;
    if (!videoUrl) {
      return client.replyMessage({
        replyToken: event.replyToken,
        messages: [{ type: "text", text: "Please provide a valid video URL.", quoteToken: event.message.quoteToken }],
      });
    } else if (!videoUrl.startsWith("https://")) {
      return client.replyMessage({
        replyToken: event.replyToken,
        messages: [{ type: "text", text: "Invalid URL. Please provide a valid video URL.", quoteToken: event.message.quoteToken }],
      });
    }


    const ytdlp = "./bin/yt-dlp.exe"
    const metadata = await getVideoMetadata(videoUrl);
    
    if (!metadata) {
      return client.replyMessage({
        replyToken: event.replyToken,
        messages: [{ type: "text", text: "Failed to fetch video metadata. Please try again later.", quoteToken: event.message.quoteToken }],
      });
    }

    if (metadata.is_live) {
      return client.replyMessage({
        replyToken: event.replyToken,
        messages: [{ type: "text", text: "You cannot download live videos.", quoteToken: event.message.quoteToken }],
      });
    }

    if (metadata.availability === "private") {
      return client.replyMessage({
        replyToken: event.replyToken,
        messages: [{ type: "text", text: "This video is private and cannot be downloaded.", quoteToken: event.message.quoteToken }],
      });
    }

    let user;
    if (event.source.type === "group") {
      user = await client.getGroupMemberProfile(event.source.groupId, event.source.userId);
    } else {
      user = await client.getProfile(event.source.userId);
    }

    const userProfile = user.displayName;
    const downloadsDir = "./static/downloads";
    if (!fs.existsSync(downloadsDir)) fs.mkdirSync(downloadsDir);

    const timestamp = Date.now();
    const outputFilePath = path.join(downloadsDir, `${timestamp}.mp4`);

    const ytDlpProcess = spawn(ytdlp, ["-f", "best", "-o", outputFilePath, videoUrl]);

    ytDlpProcess.stdout.on("data", (data) => console.log(`yt-dlp: ${data}`));
    ytDlpProcess.stderr.on("data", (data) => console.error(`yt-dlp error: ${data}`));

    ytDlpProcess.on("close", async (code) => {
      if (code !== 0) {
        console.error(`yt-dlp exited with code ${code}`);
        return client.replyMessage({
          replyToken: event.replyToken,
          messages: [{ type: "text", text: "Failed to download the video. Please try again later.", quoteToken: event.message.quoteToken }],
        });
      }

      if (fs.statSync(outputFilePath).size > maxFileSize) {
        fs.unlink(outputFilePath, (err) => {
          if (err) console.error("Error deleting file:", err);
          else console.log("File deleted successfully");
        });
        return client.replyMessage({
          replyToken: event.replyToken,
          messages: [{ type: "text", text: "Video size exceeds the maximum allowed size of 200 MB.", quoteToken: event.message.quoteToken }],
        });
      }

      let previewImageUrl = metadata.thumbnail || generateSecureUrl("/image/line.png");

      // Generate secure URL for the video file
      const videoUrl = generateSecureUrl(`/downloads/${timestamp}.mp4`, 120); // 2 hours expiry

      const messageContent = [
        {
          type: "video",
          originalContentUrl: videoUrl, // Use secure URL
          previewImageUrl,
        },
        {
          type: "text",
          text: `📼 ${metadata.title}\n${formatDuration(metadata.duration)}\n\nRequested by ${userProfile}`,
          quoteToken: event.message.quoteToken,
        },
      ];

      client.replyMessage({ replyToken: event.replyToken, messages: messageContent });
    });
  },
};

function extractVideoId(url) {
  const videoIdMatch = url.match(/(?:https?:\/\/)?(?:www\.)?youtube\.com\/watch\?v=([a-zA-Z0-9_-]{11})|(?:https?:\/\/)?youtu\.be\/([a-zA-Z0-9_-]{11})/);
  return videoIdMatch ? videoIdMatch[1] || videoIdMatch[2] : null;
}

async function getVideoMetadata(url) {
  return new Promise((resolve) => {
    const ytdlp = "./bin/yt-dlp.exe"
    let metadata = "";
    const process = spawn(ytdlp, ["--dump-json", url]);

    process.stdout.on("data", (data) => (metadata += data));
    process.stderr.on("data", (data) => console.error(`yt-dlp metadata error: ${data}`));

    process.on("close", () => {
      try {
        resolve(JSON.parse(metadata));
      } catch {
        resolve(null);
      }
    });
  });
}

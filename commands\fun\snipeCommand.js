const { QuickDB } = require("quick.db");
const db = new QuickDB();

// Helper function to format date and time
function formatDateTime(timestamp) {
  const messageDate = new Date(timestamp);
  const now = new Date();
  const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
  const yesterday = new Date(today.getTime() - 24 * 60 * 60 * 1000);
  const messageDay = new Date(messageDate.getFullYear(), messageDate.getMonth(), messageDate.getDate());

  const timeStr = messageDate.toLocaleTimeString('en-US', {
    hour12: false,
    hour: '2-digit',
    minute: '2-digit'
  });

  if (messageDay.getTime() === today.getTime()) {
    return `today at ${timeStr}`;
  } else if (messageDay.getTime() === yesterday.getTime()) {
    return `yesterday at ${timeStr}`;
  } else {
    // For older dates, show the date
    const dateStr = messageDate.toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric'
    });
    return `${dateStr} at ${timeStr}`;
  }
}

module.exports = {
  command: "snipe",
  aliases: [],
  category: "fun",
  description: "Show deleted messages from mentioned user (!snipe @user [order])\nOrder options: 'sent' (default) or 'deleted'",
  requiresPrefix: true,
  includes: false,
  handler: async (client, blobClient, event, args) => {
    // Only works in groups
    if (event.source.type !== "group") {
      return client.replyMessage({
        replyToken: event.replyToken,
        messages: [
          {
            type: "text",
            text: "❌ Snipe command only works in group chats!",
            quoteToken: event.message.quoteToken,
          },
        ],
      });
    }

    // Check if user mentioned someone
    if (!event.message.mention || !event.message.mention.mentionees || event.message.mention.mentionees.length === 0) {
      return client.replyMessage({
        replyToken: event.replyToken,
        messages: [
          {
            type: "text",
            text: "❌ Please mention a user to snipe their deleted messages!\nUsage: !snipe @username [order]\nOrder options: 'sent' (default) or 'deleted'",
            quoteToken: event.message.quoteToken,
          },
        ],
      });
    }

    // Parse order argument (default to 'sent')
    const orderBy = args.length > 0 && args[0].toLowerCase() === 'deleted' ? 'deleted' : 'sent';

    const targetUserId = event.message.mention.mentionees[0].userId;
    const groupId = event.source.groupId;
    
    // Get user's messages
    const userMessagesKey = `user_messages_${groupId}_${targetUserId}`;
    const userMessageIds = await db.get(userMessagesKey) || [];
    
    if (userMessageIds.length === 0) {
      return client.replyMessage({
        replyToken: event.replyToken,
        messages: [
          {
            type: "text",
            text: "❌ No messages found for this user!",
            quoteToken: event.message.quoteToken,
          },
        ],
      });
    }

    // Find deleted messages within the last 2 hours
    const twoHoursAgo = Date.now() - (2 * 60 * 60 * 1000);
    const deletedMessages = [];

    for (const messageId of userMessageIds) {
      const messageData = await db.get(`message_${groupId}_${messageId}`);
      if (messageData && messageData.deleted && messageData.deletedAt > twoHoursAgo) {
        deletedMessages.push(messageData);
      }
    }

    if (deletedMessages.length === 0) {
      return client.replyMessage({
        replyToken: event.replyToken,
        messages: [
          {
            type: "text",
            text: "❌ No deleted messages found within the last 2 hours!",
            quoteToken: event.message.quoteToken,
          },
        ],
      });
    }

    // Sort messages based on user preference
    if (orderBy === 'deleted') {
      // Sort by deletion time (most recent first)
      deletedMessages.sort((a, b) => b.deletedAt - a.deletedAt);
    } else {
      // Sort by original send time (chronological order - oldest first)
      deletedMessages.sort((a, b) => a.timestamp - b.timestamp);
    }

    const messagesToShow = deletedMessages.slice(0, 5);

    // Get target user's profile for display name
    let targetUserName = "Unknown User";
    try {
      const profile = await client.getGroupMemberProfile(groupId, targetUserId);
      targetUserName = profile.displayName;
    } catch (error) {
      console.error("Error getting user profile:", error);
    }

    // Format the messages with character limit handling
    const messages = [];
    let currentMessage = `🎯 Sniped messages from ${targetUserName}:\n\n`;
    const orderText = orderBy === 'deleted' ? 'by deletion time' : 'chronologically';
    const footer = `\n📝 Showing ${messagesToShow.length} of ${deletedMessages.length} deleted messages (${orderText})`;
    const maxLength = 2000;
    let truncated = false;

    for (let index = 0; index < messagesToShow.length; index++) {
      const msg = messagesToShow[index];
      const timeStr = formatDateTime(msg.timestamp); // Use original send time for display

      const messageEntry = `${index + 1}. ${timeStr}: "${msg.text}"\n`;

      // Check if adding this message would exceed the limit
      const potentialLength = currentMessage.length + messageEntry.length + footer.length;

      if (potentialLength > maxLength && currentMessage.trim() !== `🎯 Sniped messages from ${targetUserName}:`) {
        // Add current message to array and start a new one
        messages.push({
          type: "text",
          text: currentMessage.trim(),
          quoteToken: event.message.quoteToken,
        });

        // Start new message (only if we haven't reached 5 message limit)
        if (messages.length < 4) { // Keep space for footer message
          currentMessage = `🎯 Continued...\n\n${messageEntry}`;
        } else {
          // If we're at the limit, truncate and add footer to last message
          const lastMessage = messages[messages.length - 1];
          lastMessage.text += `\n\n⚠️ More messages truncated due to length limit`;
          truncated = true;
          break;
        }
      } else {
        currentMessage += messageEntry;
      }
    }

    // Add the final message with footer
    if (currentMessage.trim() !== `🎯 Sniped messages from ${targetUserName}:`) {
      if (!truncated) {
        currentMessage += footer;
      }
      messages.push({
        type: "text",
        text: currentMessage,
        quoteToken: event.message.quoteToken,
      });
    }

    // Ensure we don't exceed 5 messages
    const finalMessages = messages.slice(0, 5);

    return client.replyMessage({
      replyToken: event.replyToken,
      messages: finalMessages,
    });
  },
};

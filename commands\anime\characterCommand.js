const axios = require("axios");

module.exports = {
  command: "character",
  aliases: ["char"],
  category: "anime",
  description: "Cari info Character",
  requiresPrefix: true,
  includes: false,
  handler: async (client, blobClient, event, args) => {
    const query = args.join(" ");
    if (!query)
      return client.replyMessage({
        replyToken: event.replyToken,
        messages: [
          {
            type: "text",
            text: "Masukkan nama character yang ingin dicari!",
            quoteToken: event.message.quoteToken,
          },
        ],
      });
    const getCharacterSearch = await axios(
      `https://api.jikan.moe/v4/characters?q=${encodeURIComponent(
        query
      )}&limit=12&page=1`
    );
    if (getCharacterSearch.data.data.length === 0 || !getCharacterSearch)
      return client.replyMessage({
        replyToken: event.replyToken,
        messages: [
          {
            type: "text",
            text: "Character tidak ditemukan!",
            quoteToken: event.message.quoteToken,
          },
        ],
      });
    const messageContent = {
      type: "flex",
      altText: "MAL search result",
      contents: {
        type: "carousel",
        contents: getCharacterSearch.data.data.map((character) => ({
          type: "bubble",
          size: "hecto",
          hero: {
            type: "image",
            url: character.images.jpg.image_url,
            size: "full",
            aspectRatio: "2:3",
            aspectMode: "cover",
          },
          body: {
            type: "box",
            layout: "vertical",
            spacing: "sm",
            contents: [
              {
                type: "text",
                text: character.name,
                wrap: true,
                weight: "bold",
                size: "sm",
              },
              {
                type: "box",
                layout: "baseline",
                contents: [
                  {
                    type: "text",
                    text: character.name_kanji ? character.name_kanji : "None",
                    wrap: true,
                    size: "sm",
                    color: "#999999",
                  },
                ],
              },
            ],
          },
          footer: {
            type: "box",
            layout: "vertical",
            spacing: "sm",
            contents: [
              {
                type: "button",
                style: "link",
                height: "sm",
                action: {
                  type: "uri",
                  label: "MyAnimeList",
                  uri: `https://myanimelist.net/character/${character.mal_id}`,
                },
                color: "#4DA8DA", // blue
              },
              {
                type: "button",
                style: "primary",
                height: "sm",
                action: {
                  type: "postback",
                  label: "See Details",
                  data: `characterData=${character.mal_id}`,
                  displayText: `See Details ${character.name}`,
                },
                color: "#FF6B9D", // pink
              },
            ],
          },
        })),
      },
    };

    client.replyMessage({
      replyToken: event.replyToken,
      messages: [messageContent],
    });
  },
};

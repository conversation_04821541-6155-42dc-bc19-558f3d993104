const fs = require("fs");
const Table = require("cli-table3");

module.exports.loadPostbacks = (client) => {
  // Initialize as Map
  client.postbacks = new Map();
  client.cooldowns = new Map(); // No more Enmap config

  const table = new Table({
    head: ["Postback Data", "Status", "Time Loaded"],
    colWidths: [30, 10, 15],
  });

  const startTime = Date.now();

  fs.readdirSync("./postbacks")
    .filter((file) => file.endsWith(".js"))
    .forEach((file) => {
      const postback = require(`../postbacks/${file}`);
      const timeLoaded = ((Date.now() - startTime) / 1000).toFixed(2) + "s";

      table.push([postback.postbackData, "Loaded", timeLoaded]);
      client.postbacks.set(postback.postbackData, postback);
    });

  console.log(table.toString());
};
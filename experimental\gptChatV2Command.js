const axios = require("axios");
const { QuickDB } = require("quick.db");
const db = new QuickDB();

module.exports = {
  command: "chat2",
  aliases: ["gpt2"],
  category: "utility",
  description: "Menggunakan G4F API untuk mendapatkan balasan",
  requiresPrefix: true,
  includes: false,
  handler: async (client, blobClient, event, args) => {
    if (!args.length) {
      await client.replyMessage({
        replyToken: event.replyToken,
        messages: [
          { 
            type: "text", 
            text: "Kirimkan pesan untuk diproses: !chat2 <pesan>",
            quoteToken: event.message.quoteToken,
          }
        ]
      });
      return;
    }

    let userId = event.source.userId;
    if (event.source.type === "group") {
      userId = `${event.source.groupId}`;
    }
   
    let user;
    let group;

    if (event.source.type === "group") {
      user = await client.getGroupMemberProfile(event.source.groupId, event.source.userId);
      group = await client.getGroupSummary(event.source.groupId);
    } else {
      user = await client.getProfile(event.source.userId);
      group = "You're in one-on-one chat with user"
    }

    const userMessage = args.join(" ");
    const userName = user.displayName;
    const groupName = group.groupName;

    // Ambil riwayat percakapan sebelumnya dari database
    let previousMessages = (await db.get(`lastMessage_${userId}`)) || [];

    // Hapus riwayat paling lama jika sudah mencapai 10
    if (previousMessages.length >= 40) {
      previousMessages.shift();
    }

    // Tambahkan pesan pengguna ke riwayat
    previousMessages.push({
      role: "user",
      content:`Be concise, reply without using any formatting or codeblock
      
      ${userName}: "${userMessage}"`,
    });

    try {
      // Kirim pesan ke API Python
      const response = await axios.post("http://localhost:5000/chat", {
        messages: [
          { 
            role: "system", 
            content: `
              Introduction:
              You're a bot named BaquaBot created by Tama-chan for LINE Messanger Group chat. and you can speak indonesian, english and japanese.
              Respond in plain text only. Do not use markdown, code blocks, bullet points, or any special formatting. All responses should be written as continuous text.
              
              Task:
              - Chat as naturally as possible with the user.
              - Simulate human conversation with the user.
              - Provide reliable sources of information such as Wikipedia.
              - When users ask for your command list, tell them to type "!help".
              - Occasionally roast users humorously.
              - Avoid replying in Spanish unless initiated by the user.
              - Be fun

              Current User:
              - Name: ${userName}
              - Group Name: ${groupName}
            `
          },
          ...previousMessages
        ]
      });

      const reply = response.data

      // Kalo di chat balas yang bener lah bjir
      previousMessages.push({
        role: "assistant",
        content: reply,
      });

      // Store to database
      await db.set(`lastMessage_${userId}`, previousMessages);

      // Abuse API : Let the laptop explode
      await client.replyMessage({
        replyToken: event.replyToken,
        messages: [
          { 
            type: "text", 
            text: reply,
            quoteToken: event.message.quoteToken,
          }
        ]
      });
    } catch (error) {
      console.error("Error contacting Python API:", error.message);
      await client.replyMessage({
        replyToken: event.replyToken,
        messages: [
          { 
            type: "text", 
            text: "Terjadi kesalahan saat memproses pesan Anda.",
            quoteToken: event.message.quoteToken,
          }
        ]
      });
    }
  }
};

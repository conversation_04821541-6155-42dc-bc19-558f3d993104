const handleEcho = require("../handlers/echoHandler");
const handleAkinator = require("../handlers/akinatorHandler");
const handleTraceMoe = require("../handlers/traceMoeHandler")
const handleSauceNao = require("../handlers/saucenaoHandler")
const handleHirasen = require("../handlers/hirasenHandler");
const handleWordguess = require("../handlers/wordguessHandler");
const handleTrivia = require("../handlers/triviaHandler");
const handleOcr = require("../handlers/ocrHandler");
module.exports = async (client, blobClient, event) => {
  // Always check if the content handler can process this event, even for non-text messages

  const isTraceMoeHandled = await handleTraceMoe(client, blobClient, event);
  if (isTraceMoeHandled) {
    return true; // TraceMoe handled
  }
  const isSauceNaoHandled = await handleSauceNao(client, blobClient, event);
  if (isSauceNaoHandled) {
    return true; // <PERSON>uceNao handled
  }
  const isOcrHandled = await handleOcr(client, blobClient, event);
  if (isOcrHandled) {
    return true; // OCR handled
  }

  // Now, only proceed with text messages for echo and akinator modes
  if (event.message.type !== "text") {
    return true; // Ignore non-text messages if content mode isn't active
  }

  // Check if Echo or Akinator mode is active first
  const isEchoHandled = await handleEcho(client, event);
  if (isEchoHandled) {
    return true; // Echo mode handled, no need to process further
  }

  const isAkinatorHandled = await handleAkinator(client, event);
  if (isAkinatorHandled) {
    return true; // Akinator mode handled, no need to process further
  }
  const isHirasenHandled = await handleHirasen(client, event); // <-- Cek handler Hirasen
  if (isHirasenHandled) return true; // Jika Hirasen menangani pesan, hentikan 
  
  const isWordguessHandled = await handleWordguess(client, event);
  if (isWordguessHandled) return true; // Jika Wordguess menangani pesan, hentikan

  const isTriviaHandled = await handleTrivia(client, event); // <-- Cek handler Trivia
  if (isTriviaHandled) return true; // Jika Trivia menangani, selesai

  // Jika tidak ada handler spesifik yang menangani, baru proses sebagai command biasa
  return false; // None of the handlers took action, proceed with command processing
};

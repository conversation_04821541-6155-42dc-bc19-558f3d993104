const fs = require("fs");
const path = require("path");
const { middleware } = require("@line/bot-sdk");
const Table = require("cli-table3");

function loadEvents(client, blobClient, app, config) {
  const events = {};
  const eventsPath = path.join(__dirname, "../events");
  const startTime = Date.now();

  // Inisialisasi tabel CLI
  const table = new Table({
    head: ["Event Type", "Status", "Time Loaded"],
    colWidths: [20, 10, 15],
  });

  // Load setiap file di folder events
  fs.readdirSync(eventsPath).forEach((file) => {
    const eventModule = require(path.join(eventsPath, file));
    const eventType = path.basename(file, ".js");
    const timeLoaded = ((Date.now() - startTime) / 1000).toFixed(2) + "s";

    // Tambahkan data ke tabel
    table.push([eventType, "Loaded", timeLoaded]);

    // Simpan event handler ke dalam objek events
    events[eventType] = eventModule;
  });

  // Tampilkan tabel di console
  console.log(table.toString());

  // Definisikan route callback
  app.post(
    "/callback",
    middleware(config),
    async (req, res) => {
      await Promise.all(
        req.body.events.map(async (event) => {
          const eventType = event.type;

          // Periksa apakah event handler tersedia
          if (events[eventType]) {
            await events[eventType](event, client, blobClient);
          } else {
            console.log(`[WARN] No handler for event type: ${eventType}`);
          }
        })
      )
        .then((result) => res.json(result))
        .catch((err) => {
          console.error(`[ERROR] ${err.message}`);
          res.status(500).end();
        });
    }
  );
}

module.exports = { loadEvents };

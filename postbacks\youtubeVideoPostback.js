const { spawn } = require("child_process"); // Import spawn from child_process
const fs = require("fs");
const path = require("path");
const { formatDuration } = require("../utils/utils");
const { generateSecureUrl } = require("../utils/urlSigner");

const ytDlpBinary = "./bin/yt-dlp.exe"; // Define the path to your yt-dlp executable

module.exports = {
  postbackData: "videoYoutubeData=", // Prefix for dynamic sticker postbacks
  cooldown: 10,
  handler: async (client, blobClient, event, id) => {
    const videoUrl = id; // Use dynamic URL from args if provided
    const maxFileSize = 200 * 1024 * 1024;
    const videoId = extractVideoId(videoUrl);

    // Helper function to get video metadata using yt-dlp with --dump-json
    const getVideoInfo = (url) => {
      return new Promise((resolve, reject) => {
        const infoProcess = spawn(ytDlpBinary, [url, "--dump-json"],
          { windowsHide: true }
        );
        let output = "";
        let errorOutput = "";

        infoProcess.stdout.on("data", (data) => {
          output += data.toString();
        });

        infoProcess.stderr.on("data", (data) => {
            errorOutput += data.toString();
        });
        
        infoProcess.on("close", (code) => {
          if (code !== 0) {
            reject(new Error(`yt-dlp failed to fetch video info. Stderr: ${errorOutput}`));
          } else {
            try {
              const info = JSON.parse(output);
              resolve(info);
            } catch (err) {
              reject(err);
            }
          }
        });

        infoProcess.on("error", (err) => {
          reject(err);
        });
      });
    };
    
    let metadata;
    try {
      metadata = await getVideoInfo(videoUrl);

      if (metadata.is_live) {
        return client.replyMessage({
          replyToken: event.replyToken,
          messages: [
            { type: "text", text: "You cannot download live videos." },
          ],
        });
      }

      if (metadata.availability === "private") {
        return client.replyMessage({
          replyToken: event.replyToken,
          messages: [{
            type: "text",
            text: "This video is private and cannot be downloaded.",
          }, ],
        });
      }
    } catch (error) {
      console.error("Failed to fetch video metadata:", error);
      return client.replyMessage({
        replyToken: event.replyToken,
        messages: [{
          type: "text",
          text: "Failed to fetch video metadata. Please try again later.",
        }, ],
      });
    }

    let user;
    if (event.source.type === "group") {
      user = await client.getGroupMemberProfile(
        event.source.groupId,
        event.source.userId
      );
    } else {
      user = await client.getProfile(event.source.userId);
    }
    const userProfile = user.displayName;

    const downloadsDir = "./static/downloads";
    if (!fs.existsSync(downloadsDir)) {
      fs.mkdirSync(downloadsDir, { recursive: true });
    }

    const timestamp = Date.now();
    const outputFilePath = path.join(downloadsDir, `${timestamp}.mp4`);

    const downloadProcess = spawn(ytDlpBinary, [
      videoUrl, // Use the provided video URL
      "-f", "best", // Download the best quality format
      "-o", outputFilePath, // Save the video to the specified path
    ],
      { windowsHide: true }
    );

    downloadProcess.stdout.on('data', (data) => {
        console.log(`yt-dlp stdout: ${data}`);
    });

    downloadProcess.stderr.on('data', (data) => {
        console.error(`yt-dlp stderr: ${data}`);
    });

    downloadProcess.on("error", (error) => {
        console.error("Failed to start download process:", error);
    });

    downloadProcess.on("close", (code) => {
        if (code !== 0) {
            console.error(`yt-dlp process exited with code ${code}`);
            return client.replyMessage({
              replyToken: event.replyToken,
              messages: [{
                type: "text",
                text: "Failed to download the video.",
              }, ],
            });
          }

        if (!fs.existsSync(outputFilePath)) {
            console.error("Output file not found after download.");
            return;
        }

        if (fs.statSync(outputFilePath).size > maxFileSize) {
            fs.unlink(outputFilePath, (err) => {
              if (err) {
                console.error("Error deleting oversized file:", err);
              } else {
                console.log("Oversized file deleted successfully");
              }
            });
            return client.replyMessage({
              replyToken: event.replyToken,
              messages: [{
                type: "text",
                text: "Video size exceeds the maximum allowed size of 200 MB.",
              }, ],
            });
        }
        
        let previewImageUrl = "";
        if (videoId) {
            previewImageUrl = `https://to-jpg.vercel.app/convert?url=${metadata.thumbnail}&format=jpg`;
        } else if (metadata.thumbnail) {
            previewImageUrl = metadata.thumbnail;
        } else if (metadata.thumbnails && metadata.thumbnails[0] && metadata.thumbnails[0].url) {
            previewImageUrl = metadata.thumbnails[0].url;
        } else {
            previewImageUrl = generateSecureUrl("/image/line.png");
        }

        const secureVideoUrl = generateSecureUrl(`/downloads/${timestamp}.mp4`, 120); 

        const messageContent = [{
            type: "video",
            originalContentUrl: secureVideoUrl, 
            previewImageUrl: previewImageUrl,
        }, {
            type: "text",
            text: `📼 ${metadata.title}\n${formatDuration(metadata.duration)}\n\nRequested by ${userProfile}`,
        }, ];

        client.replyMessage({
            replyToken: event.replyToken,
            messages: messageContent,
        });
    });
  },
};

function extractVideoId(url) {
  const videoIdMatch = url.match(
    /(?:https?:\/\/)?(?:www\.)?youtube\.com\/watch\?v=([a-zA-Z0-9_-]{11})|(?:https?:\/\/)?youtu\.be\/([a-zA-Z0-9_-]{11})/
  );
  return videoIdMatch ? videoIdMatch[1] || videoIdMatch[2] : null;
}
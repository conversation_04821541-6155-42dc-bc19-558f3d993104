const { HAnimeAPI } = require("hanime");
const api = new HAnimeAPI();
const { format } = require('date-fns');
const util = require("../utils/utils");

module.exports = {
  postbackData: "hanimeData=",
  cooldown: 10,
  handler: async (client, blobClient, event, id) => {
    const results = await api.search(id); 
    const video = results.videos[0];

    const replyText =
      `Title : ${video.name}\n` +
      `Brand : ${video.brand}\n\n` +
      `Synopsis : ${video.description.replace(/<\/?[^>]+(>|$)/g, "")}\n\n` +
      `Views : ${video.views}\n` +
      `Genres : ${video.tags.join(", ")}\n` + 
      `${video.duration_in_ms === 0 ? "" : `Duration : ${util.formatMilliseconds(video.duration_in_ms)}`}\n` +
      `Released : ${format(new Date(video.released_at * 1000), "yyyy-MM-dd")}\n` +
      `Uploaded : ${format(new Date(video.created_at * 1000), "yyyy-MM-dd")}\n` +
      `Rating : ${video.rating}\n` +
      `Likes : ${video.likes}\n` +
      `Dislikes : ${video.dislikes}\n` +
      `Link : https://hanime.tv/videos/hentai/${video.slug}`;

    client.replyMessage({
      replyToken: event.replyToken,
      messages: [
        {
          type: "image",
          originalContentUrl: video.cover_url,
          previewImageUrl: video.cover_url
        },
        {
          type: "text",
          text: replyText,
        }
      ]
    });
  },
};
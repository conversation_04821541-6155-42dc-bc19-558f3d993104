const { QuickDB } = require("quick.db");
const db = new QuickDB();
const axios = require("axios");
const { setSessionTimeout } = require("../../utils/sessionTimeouts");

module.exports = {
  command: "akinator",
  aliases: [],
  category: "fun",
  description: "Play Akinator: The bot will try to guess the character you are thinking of.",
  requiresPrefix: true,
  includes: false,
  handler: async (client, blobClient, event, args) => {
    const userId = event.source.userId;
    const groupId = event.source.groupId || event.source.roomId;

    const isGameActiveInGroup = await db.get(`${groupId}_akinatorActive`);
    if (!isGameActiveInGroup) {
      try {
        const res = await axios.post("http://localhost:5000/akinator/start");
        const { session_id, question } = res.data;

        await db.set(`${groupId}_akinatorActive`, true);
        await db.set(`${groupId}_akinatorOwner`, userId);
        await db.set(`${groupId}_akinatorSession`, session_id);
        await db.set(`${groupId}_akinatorStep`, 0);

        client.replyMessage({
          replyToken: event.replyToken,
          messages: [
            {
              type: "text",
              text: `Think of a character and answer my questions!\nQuestion 1: ${question}\nAnswers:\n1. Yes\n2. No\n3. Don't know\n4. Probably\n5. Probably not\n\n... or type "/end" to end the game`,
              quoteToken: event.message.quoteToken,
            },
          ],
        });

        setSessionTimeout(groupId, async () => {
          await db.delete(`${groupId}_akinatorActive`);
          await db.delete(`${groupId}_akinatorOwner`);
          await db.delete(`${groupId}_akinatorSession`);
          await db.delete(`${groupId}_akinatorStep`);
        });
      } catch (err) {
        client.replyMessage({
          replyToken: event.replyToken,
          messages: [
            {
              type: "text",
              text: "Failed to start Akinator game. Please try again later.",
              quoteToken: event.message.quoteToken,
            },
          ],
        });
        console.error("Error starting Akinator game:", err);
      }
    } else {
      client.replyMessage({
        replyToken: event.replyToken,
        messages: [
          {
            type: "text",
            text: "A game is already active in this group. Please wait for it to finish or type '/end' to end the current game.",
            quoteToken: event.message.quoteToken,
          },
        ],
      });
    }
  },
};

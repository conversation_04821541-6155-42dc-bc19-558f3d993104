FROM node:23
# Consider pinning the base image to a specific digest for production to ensure immutability:
# FROM node@sha256:<your-digest-here>

# Investigate and address the high vulnerability reported in the base image.
# You might need to update base OS packages or consider a different base image if necessary.

WORKDIR /app

COPY package*.json ./

RUN npm install --omit=dev

COPY . .

EXPOSE 3000

CMD [ "npm", "start" ]
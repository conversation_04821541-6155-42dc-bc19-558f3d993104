from .chat import chat_bp
from .gemini_AI import gemini_ai_bp
from .image import image_bp
from .akinator import akinator_bp
from .file import file_bp

def register_routes(app):
    app.register_blueprint(chat_bp, url_prefix="/")
    app.register_blueprint(image_bp, url_prefix="/")
    app.register_blueprint(akinator_bp, url_prefix="/akinator")
    app.register_blueprint(file_bp, url_prefix="/")
    app.register_blueprint(gemini_ai_bp, url_prefix="/api")

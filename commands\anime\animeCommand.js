const axios = require("axios");

module.exports = {
  command: "animes",
  aliases: ["ani"],
  category: "anime",
  description: "Cari info anime",
  requiresPrefix: true, 
  includes: false, 
  handler: async (client, blobClient, event, args) => {
    const query = args.join(" ")
    if (!query) return client.replyMessage({
      replyToken: event.replyToken,
      messages: [
        {
        type: "text",
        text: "Masukkan judul anime yang ingin dicari",
        quoteToken: event.message.quoteToken,
        }
    ]
    });
    const getAnimeSearch = await axios(`https://api.jikan.moe/v4/anime?q=${encodeURIComponent(query)}&limit=12&page=1`);
    if (getAnimeSearch.data.data.length === 0 || !getAnimeSearch) return client.replyMessage({
      replyToken: event.replyToken,
      messages: [
        {
          type: "text",
          text: `Anime not found : ${query}`,
          quoteToken: event.message.quoteToken,
        },
      ],
    });
    const messageContent = {
      type: "flex",
      altText: "MAL search result",
      contents: {
        type: "carousel",
        contents: getAnimeSearch.data.data.map((anime) => ({
          type: "bubble",
          size: "hecto",
          hero: {
            type: "image",
            url: anime.images.jpg.large_image_url,
            size: "full",
            aspectRatio: "2:3",
            aspectMode: "cover",
          },
          body: {
            type: "box",
            layout: "vertical",
            spacing: "sm",
            contents: [
              {
                type: "text",
                text: anime.title,
                wrap: true,
                weight: "bold",
                size: "sm",
              },
              {
                type: "box",
                layout: "baseline",
                contents: [
                  {
                    type: "text",
                    text: anime.type,
                    wrap: true,
                    size: "sm",
                    color: "#999999",
                  },
                ],
              },
            ],
          },
          footer: {
            type: "box",
            layout: "vertical",
            spacing: "sm",
            contents: [
              {
                type: "button",
                style: "primary",
                height: "sm",
                action: {
                  type: "uri",
                  label: "MyAnimeList",
                  uri: `https://myanimelist.net/anime/${anime.mal_id}`,
                },
                color: "#4DA8DA",
              },
              {
                type: "button",
                style: "primary",
                height: "sm",
                action: {
                  type: "postback",
                  label: "See Details",
                  data: `animeData=${anime.mal_id}`,
                  displayText: `See Details ${anime.title}`,
                },
                color: "#FF6B9D", // pink
              },
            ],
          },
        })),
      },
    };
   
    client.replyMessage({
      replyToken: event.replyToken,
      messages: [messageContent]
    });
  },
};

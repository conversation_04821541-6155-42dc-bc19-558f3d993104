const axios = require("axios");
const utils = require("../utils/utils")
module.exports = {
  postbackData: "animeData=", // Prefix for dynamic sticker postbacks
  cooldown: 10,
  handler: async (client, blobClient, event, id) => {
    
    const animeFullById = await axios(`https://api.jikan.moe/v4/anime/${id}/full`)
    const replyText =
      `Title: ${animeFullById.data.data.title || "Unknown"}\n` +
      `Japanese: ${animeFullById.data.data.title_japanese || "Unknown"}\n` +
      `Synonyms: ${animeFullById.data.data.title_synonyms.length > 0 ? animeFullById.data.data.title_synonyms.join(", ") : "None"}\n` +
      `Type: ${animeFullById.data.data.type || "Unknown"}\n` +
      `Season: ${animeFullById.data.data.season || "Unknown"}\n` +
      `Year: ${animeFullById.data.data.year || "Unknown"}\n` +
      `Episodes: ${animeFullById.data.data.episodes || "Unknown"}\n` +
      `Duration: ${animeFullById.data.data.duration || "Unknown"}\n` +
      `Status: ${animeFullById.data.data.status || "Unknown"}\n` +
      `Score: ${animeFullById.data.data.score || "N/A"}\n` +
      `Rating: ${animeFullById.data.data.rating || "Unrated"}\n` +
      `Synopsis: ${utils.TruncateText(animeFullById.data.data.synopsis || "No synopsis available.", 3000)}\n` +
      `Genres: ${animeFullById.data.data.genres.length > 0 ? animeFullById.data.data.genres.map(genre => genre.name).join(', ') : "Unknown"}\n` +
      `Studios: ${animeFullById.data.data.studios.length > 0 ? animeFullById.data.data.studios.map(studio => studio.name).join(', ') : "Unknown"}\n` +
      `Aired: ${animeFullById.data.data.aired.string || "Unknown"}\n` +
      `Source: ${animeFullById.data.data.source || "Unknown"}\n` +
      `More: ${animeFullById.data.data.url || "No additional information"}`;


    client.replyMessage({
      replyToken: event.replyToken,
      messages: [
        {
          type: "image",
          originalContentUrl: animeFullById.data.data.images.jpg.large_image_url,
          previewImageUrl: animeFullById.data.data.images.jpg.large_image_url,
        },
        {
          type: "text",
          text: replyText,
        },
      ],
    });
  },
};

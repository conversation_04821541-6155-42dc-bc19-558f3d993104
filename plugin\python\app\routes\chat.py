from flask import Blueprint, request, jsonify
from g4f.client import Client
from g4f.Provider import Copilot

chat_bp = Blueprint('chat', __name__)

@chat_bp.route('/chat', methods=['POST'])
def chat():
    try:
        data = request.json
        messages = data.get("messages")

        if not messages:
            return jsonify({"error": "Messages are required"}), 400

        client = Client(provider=Copilot)
        response = client.chat.completions.create(
        messages=messages,    
        )
        return response.choices[0].message.content
    except Exception as e:
        return jsonify({"error": str(e)}), 500

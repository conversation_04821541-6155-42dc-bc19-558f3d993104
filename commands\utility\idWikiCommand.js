const axios = require("axios");

module.exports = {
  command: "idwiki",
  aliases: ["idw"],
  category: "utility",
  description: "Cari rangkuman wiki bahasa indonesia",
  requiresPrefix: true,
  includes: false,
  handler: async (client, blobClient, event, args) => {
    try {
      const query = args.join(" ");
      const apiUrl = `https://id.wikipedia.org/api/rest_v1/page/summary/${encodeURIComponent(
        query
      )}`;

      // Mengirim request ke API
      const response = await axios.get(apiUrl);

      // Data dari API
      const summary = response.data;

      // Cek apakah rangkuman memiliki informasi yang diperlukan
      if (!summary || !summary.extract || !summary.title) {
        return client.replyMessage({
          replyToken: event.replyToken,
          messages: [
            {
              type: "text",
              text: "❌ Halaman tidak ditemukan!",
              quoteToken: event.message.quoteToken,
              sender: {
                name: "Wikipedia Summary",
                iconUrl:
                  "https://upload.wikimedia.org/wikipedia/commons/thumb/8/80/Wikipedia-logo-v2.svg/1200px-Wikipedia-logo-v2.svg.png",
              },
            },
          ],
        });
      }

      // Jika terdapat gambar di rangkuman
      if (summary.originalimage) {
        client.replyMessage({
          replyToken: event.replyToken,
          messages: [
            {
              type: "image",
              originalContentUrl: summary.originalimage.source,
              previewImageUrl: summary.originalimage.source,
              sender: {
                name: "Wikipedia Summary",
                iconUrl:
                  "https://upload.wikimedia.org/wikipedia/commons/thumb/8/80/Wikipedia-logo-v2.svg/1200px-Wikipedia-logo-v2.svg.png",
              },
            },
            {
              type: "text",
              text: `🌐 ${summary.title}\n\n${summary.extract}\n\nInformasi lebih lanjut: ${summary.content_urls.desktop.page}`,
              quoteToken: event.message.quoteToken,
              sender: {
                name: "Wikipedia Summary",
                iconUrl:
                  "https://upload.wikimedia.org/wikipedia/commons/thumb/8/80/Wikipedia-logo-v2.svg/1200px-Wikipedia-logo-v2.svg.png",
              },
            },
          ],
        });
      } else {
        // Jika tidak ada gambar, hanya kirim teks
        client.replyMessage({
          replyToken: event.replyToken,
          messages: [
            {
              type: "text",
              text: `🌐 ${summary.title}\n\n${summary.extract}\n\nInformasi lebih lanjut: ${summary.content_urls.desktop.page}`,
              quoteToken: event.message.quoteToken,
              sender: {
                name: "Wikipedia Summary",
                iconUrl:
                  "https://upload.wikimedia.org/wikipedia/commons/thumb/8/80/Wikipedia-logo-v2.svg/1200px-Wikipedia-logo-v2.svg.png",
              },
            },
          ],
        });
      }
    } catch (error) {
      // Menangani error jika terjadi (misalnya halaman tidak ditemukan atau error API lainnya)
      console.error("Error fetching Wikipedia summary:", error.message);

      // Balas dengan pesan error
      return client.replyMessage({
        replyToken: event.replyToken,
        messages: [
          {
            type: "text",
            text: "❌ Halaman tidak ditemukan!",
            quoteToken: event.message.quoteToken,
            sender: {
              name: "Wikipedia Summary",
              iconUrl:
                "https://upload.wikimedia.org/wikipedia/commons/thumb/8/80/Wikipedia-logo-v2.svg/1200px-Wikipedia-logo-v2.svg.png",
            },
          },
        ],
      });
    }
  },
};

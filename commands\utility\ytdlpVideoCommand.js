const { spawn } = require("child_process");
const fs = require("fs");
const path = require("path");
const ffmpeg = require("fluent-ffmpeg");
const { applyVideoFilters, videoFilterMap } = require("../../utils/filters");
const { formatDuration } = require("../../utils/utils");
const { generateSecureUrl } = require("../../utils/urlSigner");

const ytDlpBinary = "./bin/yt-dlp.exe";

module.exports = {
  command: "mp4",
  aliases: ["ytv"],
  category: "utility",
  description: "Download video from links with optional filters",
  requiresPrefix: true,
  includes: false,
  handler: async (client, blobClient, event, args) => {
    const input = event.message.text.split(" ");
    const videoUrl = input[1];
    const filterInput = input.slice(2).join(" ");

    const maxFileSize = 200 * 1024 * 1024; // 200MB
    const supportedFilters = videoFilterMap ? Object.keys(videoFilterMap) : [];
    if (!videoUrl) {
      return client.replyMessage({
        replyToken: event.replyToken,
        messages: [{
          type: "text",
          text: "Please provide a valid video URL.",
          quoteToken: event.message.quoteToken,
        }, ],
      });
    } else if (!videoUrl.startsWith("https://")) {
      return client.replyMessage({
        replyToken: event.replyToken,
        messages: [{
          type: "text",
          text: "Invalid URL. Please provide a valid video URL.",
          quoteToken: event.message.quoteToken,
        }, ],
      });
    }

    // **Parsing Filters**
    let filters = [];
    let customFilters = [];

    if (filterInput) {
      const matches = filterInput.match(/\[([^\]]+)\]/);
      if (matches) {
        const rawFilters = matches[1].split(",");
        rawFilters.forEach((filter) => {
          if (filter.startsWith("custom=")) {
            customFilters.push(filter.replace("custom=", ""));
          } else if (supportedFilters.includes(filter)) {
            filters.push(filter);
          }
        });
      }
    }

    // Helper function to get video metadata
    const getVideoInfo = (url) => {
      return new Promise((resolve, reject) => {
        const infoProcess = spawn(ytDlpBinary, [url, "--dump-json"], {
          windowsHide: true,
        });
        let output = "";
        let errorOutput = "";

        infoProcess.stdout.on("data", (data) => {
          output += data.toString();
          
        });

        infoProcess.stderr.on("data", (data) => {
          errorOutput += data.toString();
          console.error(errorOutput);
        });

        infoProcess.on("close", (code) => {
          if (code !== 0) {
            reject(new Error(`yt-dlp failed to fetch video info. Stderr: ${errorOutput}`));
          } else {
            try {
              const info = JSON.parse(output);
              resolve(info);
            } catch (err) {
              reject(err);
            }
          }
        });

        infoProcess.on("error", (err) => {
          reject(err);
        });
      });
    };

    const videoId = extractVideoId(videoUrl);
    let metadata;
    try {
      metadata = await getVideoInfo(videoUrl);

      if (metadata.is_live) {
        return client.replyMessage({
          replyToken: event.replyToken,
          messages: [{
            type: "text",
            text: "You cannot download live videos.",
            quoteToken: event.message.quoteToken,
          }, ],
        });
      }

      if (metadata.availability === "private") {
        return client.replyMessage({
          replyToken: event.replyToken,
          messages: [{
            type: "text",
            text: "This video is private and cannot be downloaded.",
            quoteToken: event.message.quoteToken,
          }, ],
        });
      }
    } catch (error) {
      console.error("Failed to fetch video metadata:", error);
      return client.replyMessage({
        replyToken: event.replyToken,
        messages: [{
          type: "text",
          text: "Failed to fetch video metadata. Please try again later.",
          quoteToken: event.message.quoteToken,
        }, ],
      });
    }

    let user;
    if (event.source.type === "group") {
      user = await client.getGroupMemberProfile(
        event.source.groupId,
        event.source.userId
      );
    } else {
      user = await client.getProfile(event.source.userId);
    }
    const userProfile = user.displayName;

    const downloadsDir = "./static/downloads";
    if (!fs.existsSync(downloadsDir)) {
      fs.mkdirSync(downloadsDir, { recursive: true });
    }

    const timestamp = Date.now();
    const originalFilePath = path.join(downloadsDir, `${timestamp}_original.mp4`);
    const filteredFilePath = path.join(downloadsDir, `${timestamp}_filtered.mp4`);

    const downloadProcess = spawn(ytDlpBinary, [
      videoUrl,
      "-f", "best",
      "-o", originalFilePath,
    ],
      { windowsHide: true }
    );
    downloadProcess.stdout.on("data", (data) => {
        console.log(data.toString());
    });
    
    downloadProcess.on("close", async (code) => {
        if (code !== 0) {
            console.error(`yt-dlp process exited with code ${code}`);
            return client.replyMessage({
                replyToken: event.replyToken,
                messages: [{ type: "text", text: "Failed to download video.", quoteToken: event.message.quoteToken,},],
            });
        }
    
        let finalFilePath = originalFilePath;
        let duration = metadata.duration * 1000;
    
        if (filters.length > 0 || customFilters.length > 0) {
            try {
              await applyVideoFilters(originalFilePath, filteredFilePath, [...filters, ...customFilters]);
    
              if (!fs.existsSync(filteredFilePath) || fs.statSync(filteredFilePath).size === 0) {
                throw new Error("Filtered video file is empty or not created");
              }
    
              finalFilePath = filteredFilePath;
              duration = await new Promise((resolve, reject) => {
                ffmpeg.ffprobe(filteredFilePath, (err, data) =>
                  err ? reject(err) : resolve(data.format.duration * 1000)
                );
              });
            } catch (error) {
              console.error("Video filter error:", error);
              if (fs.existsSync(filteredFilePath)) {
                fs.unlinkSync(filteredFilePath);
              }
              return client.replyMessage({
                replyToken: event.replyToken,
                messages: [{
                  type: "text",
                  text: `❌ Failed to apply video filters: ${[...filters, ...customFilters].join(", ")}\n\nThe filters may not be compatible. Try simpler filters.`,
                  quoteToken: event.message.quoteToken,
                }, ],
              });
            }
          }

          if (fs.statSync(finalFilePath).size > maxFileSize) {
            fs.unlinkSync(finalFilePath);
            return client.replyMessage({
              replyToken: event.replyToken,
              messages: [{
                type: "text",
                text: "Video size exceeds the maximum allowed size of 200 MB.",
                quoteToken: event.message.quoteToken,
              }, ],
            });
          }

          let previewImageUrl = "";
          if (videoId) {
            previewImageUrl = `https://to-jpg.vercel.app/convert?url=${metadata.thumbnail}&format=jpg`;
          } else if (metadata.thumbnail) {
            previewImageUrl = metadata.thumbnail;
          } else if (metadata.thumbnails && metadata.thumbnails[0] && metadata.thumbnails[0].url) {
            previewImageUrl = metadata.thumbnails[0].url;
          } else {
            previewImageUrl = generateSecureUrl("/image/line.png");
          }

          const secureVideoUrl = generateSecureUrl(`/downloads/${path.basename(finalFilePath)}`, 120);
    
          const messageContent = [{
            type: "video",
            originalContentUrl: secureVideoUrl,
            previewImageUrl: previewImageUrl,
          },
          {
            type: "text",
            text: `📼 ${metadata.title}\nFilters: ${
              [...filters, ...customFilters].join(", ") || "Normal"
            }\n${formatDuration(
              metadata.duration
            )}\n\nRequested by ${userProfile}`,
            quoteToken: event.message.quoteToken,
          },];

          client.replyMessage({
            replyToken: event.replyToken,
            messages: messageContent,
          });
    
          if (filters.length > 0 || customFilters.length > 0) {
            fs.unlinkSync(originalFilePath);
          }
    });
  },
};

function extractVideoId(url) {
  const videoIdMatch = url.match(
    /(?:https?:\/\/)?(?:www\.)?youtube\.com\/watch\?v=([a-zA-Z0-9_-]{11})|(?:https?:\/\/)?youtu\.be\/([a-zA-Z0-9_-]{11})/
  );
  return videoIdMatch ? videoIdMatch[1] || videoIdMatch[2] : null;
}
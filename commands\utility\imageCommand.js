const axios = require("axios");
const path = require("path");

module.exports = {
  command: "gambar",
  aliases: ["image", "gi"],
  category: "utility",
  description: "Cari gambar di google (random)",
  requiresPrefix: true,
  includes: false,
  handler: (client, blobClient, event, args) => {
    const query = args.join(" ");
    const page = Math.floor(Math.random() * 2);
    axios
      .get(
        `https://serpapi.com/search?q=${encodeURIComponent(query)}&tbm=isch&ijn=${page}&engine=google&apikey=${process.env.serp}`
      )
      .then((response) => {
        const image =
          response.data.images_results[
            Math.floor(Math.random() * response.data.images_results.length)
          ];

        const imageUrl = image.original;
        const thumbnailUrl = image.thumbnail;
        const imageExtension = path.extname(new URL(imageUrl).pathname).toLowerCase();

        let originalContentUrl = imageUrl;
        let previewImageUrl = thumbnailUrl;

        const allowedExtensions = [".png", ".jpg", ".jpeg", ".gif"];
        if (!allowedExtensions.includes(imageExtension)) {
          originalContentUrl = `https://to-jpg.vercel.app/convert?url=${imageUrl}&format=jpg`;
          previewImageUrl = `https://to-jpg.vercel.app/convert?url=${thumbnailUrl}&format=jpg`;
        }

        client.replyMessage({
          replyToken: event.replyToken,
          messages: [
            {
              type: "image",
              originalContentUrl: originalContentUrl,
              previewImageUrl: previewImageUrl,
            },
          ],
        });
      })
      .catch((error) => {
        console.error(error);
        client.replyMessage({
          replyToken: event.replyToken,
          messages: [
            {
              type: "text",
              text: "Error when getting image.",
              quoteToken: event.message.quoteToken,
            },
          ],
        });
      });
  },
};
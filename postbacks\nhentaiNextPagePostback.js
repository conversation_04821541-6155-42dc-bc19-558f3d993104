const axios = require("axios");
module.exports = {
    postbackData: "nextBookData=",
    cooldown: 5,
    handler: async (client, blobClient, event, id) => {
      const { API, TagTypes } = require("nhentai-api");

    const api = new API({
      usePuppeteer: true,
    });
      try {
        // Extract search term and page number from postback data
        const postbackData = id.split("_");
        const searchTerm = postbackData[0];
        const currentPage = parseInt(postbackData[1], 10);
       const waifoo = await axios.get('https://api.waifu.pics/sfw/waifu')
       const nextImage = waifoo.data.url
  
        if (!searchTerm || isNaN(currentPage)) {
          return client.replyMessage({
            replyToken: event.replyToken,
            messages: [{ type: "text", text: "Invalid page data!" }],
          });
        }
  
        // Fetch search results for the requested page
        const search = await api.search(encodeURIComponent(searchTerm), currentPage);
        const books = search.books;
  
        if (!books || books.length === 0) {
          return client.replyMessage({
            replyToken: event.replyToken,
            messages: [
              { type: "text", text: `Page ${currentPage - 1} is the last page. No more results!` },
            ],
          });
        }
  
        // Maximum 12 books per carousel
        const groupedBooks = [];
        for (let i = 0; i < books.length; i += 7) {
          groupedBooks.push(books.slice(i, i + 7));
        }
  
        const flexMessages = await Promise.all(
          groupedBooks.map(async (group, index) => {
            const bubbles = await Promise.all(
                group.map(async (book) => {
                    const language = book
                      .getTagsWith({ type: TagTypes.Language })
                      .join(", ");
                              
                    // Get cover URL using the new API - no workaround needed!
                    // The new package handles multiple hosts and WebP format automatically
                    let coverURL = api.getImageURL(book.pages[0]);
                    
                    // Keep the to-jpg proxy only for LINE compatibility (LINE doesn't support WebP)
                    if (coverURL.includes('.webp')) {
                      coverURL = `https://to-jpg.vercel.app/convert?url=${encodeURIComponent(coverURL)}&format=jpg`;
                    }

                    return {
                      type: "bubble",
                      size: "hecto",
                      hero: {
                        type: "image",
                        url: coverURL,
                        size: "full",
                        aspectRatio: "2:3",
                        aspectMode: "cover",
                      },
                      body: {
                        type: "box",
                        layout: "vertical",
                        contents: [
                          {
                            type: "text",
                            text: `ID: #${book.id} | ${language}`,
                            weight: "bold",
                            size: "xs",
                            color: "#aaaaaa",
                          },
                          {
                            type: "text",
                            text: book.title.pretty,
                            weight: "bold",
                            size: "sm",
                            wrap: true,
                          },
                          {
                            type: "box",
                            layout: "vertical",
                            margin: "md",
                            contents: [
                              {
                                type: "text",
                                text:
                                  book.artists.map((artist) => artist.name).join(", ") ||
                                  "Unknown",
                                size: "xs",
                                color: "#aaaaaa",
                                wrap: true,
                              },
                              {
                                type: "text",
                                text: `${book.pages.length} pages`,
                                size: "xs",
                                color: "#aaaaaa",
                                wrap: false,
                              },
                            ],
                          },
                        ],
                      },
                      footer: {
                        type: "box",
                        layout: "vertical",
                        spacing: "sm",
                        contents: [
                          {
                            type: "button",
                            style: "primary",
                            height: "sm",
                            action: {
                              type: "uri",
                              label: "Open in nhentai",
                              uri: `https://nhentai.net/g/${book.id}/`,
                            },
                          },
                          {
                            type: "button",
                            style: "primary",
                            height: "sm",
                            action: {
                              type: "uri",
                              label: "Download",
                              uri: `https://tamauniverse.vercel.app/api/nhentai/${book.id}/pdf`,
                            },
                          },
                          {
                            type: "button",
                            style: "primary",
                            height: "sm",
                            action: {
                              type: "uri",
                              label: "Mirror Download",
                              uri: `https://associated-roberta-tama-universe-5876c21c.koyeb.app/api/nhentai/${book.id}/pdf`,
                            },
                          },
                          {
                            type: "button",
                            style: "primary",
                            height: "sm",
                            action: {
                              type: "postback",
                              label: "See Details",
                              data: `nhentaiDetailsData=${book.id}`,
                              displayText: `See Details ${book.id}`,
                            },
                          },
                        ],
                      },
                    };
                  })
            );
  
            // Add "Next Page" button only on the last flex message
            if (index === groupedBooks.length - 1) {
              bubbles.push({
                type: "bubble",
                size: "hecto",
                hero: {
                  type: "image",
                  url: nextImage,
                  size: "full",
                  aspectRatio: "2:3",
                  aspectMode: "cover",
                },
                body: {
                  type: "box",
                  layout: "vertical",
                  contents: [
                    {
                      type: "text",
                      text: `To The Next Page (${currentPage}/${search.pages})`,
                      weight: "bold",
                      wrap: true,
                      size: "lg",
                      align: "center",
                    },
                  ],
                },
                footer: {
                  type: "box",
                  layout: "vertical",
                  spacing: "sm",
                  contents: [
                    {
                      type: "button",
                      style: "primary",
                      height: "sm",
                      action: {
                        type: "postback",
                        label: "Next Page",
                        data: `nextBookData=${searchTerm}_${currentPage + 1}`,
                        displayText: `Bot generated : To page ${currentPage + 1}/${search.pages}`,
                      },
                    },
                  ],
                },
              });
            }
  
            return {
              type: "flex",
              altText: `Search results for "${searchTerm}" (Page ${currentPage})`,
              contents: {
                type: "carousel",
                contents: bubbles,
              },
            };
          })
        );
  
        await client.replyMessage({
          replyToken: event.replyToken,
          messages: flexMessages.slice(0, 5),
        });

      } catch (error) {
        console.error(error);
        return client.replyMessage({
          replyToken: event.replyToken,
          messages: [{ type: "text", text: "Error fetching search results. Please try again later." }],
        });
      }
    },
  };
const { QuickDB } = require("quick.db");
const { setSessionTimeout, clearSessionTimeout } = require("../utils/sessionTimeouts");
const db = new QuickDB();

module.exports = async (client, event) => {
  if (event.message.type !== "text") {
    return false;
  }

  const userId = event.source.userId;
  const groupId = event.source.groupId || event.source.roomId || userId;
  const sessionId = `${groupId}_${userId}`;
  const userMessage = event.message.text.trim();

  // Check if there's an active sysprompt session for this user
  const isSessionActive = await db.get(`syspromptActive_${sessionId}`);
  const sessionOwner = await db.get(`syspromptOwner_${sessionId}`);

  if (!isSessionActive || userId !== sessionOwner) {
    return false; // Not handling this message
  }

  // Clear the current session timeout
  clearSessionTimeout(sessionId);

  let user;
  try {
    if (event.source.type === "group") {
      user = await client.getGroupMemberProfile(event.source.groupId, event.source.userId);
    } else {
      user = await client.getProfile(event.source.userId);
    }
  } catch (error) {
    console.error("Error getting user profile:", error);
    user = { displayName: "User" };
  }

  const userName = user.displayName;

  try {
    // Handle special commands
    if (userMessage.toLowerCase() === "/cancel") {
      await db.delete(`syspromptActive_${sessionId}`);
      await db.delete(`syspromptOwner_${sessionId}`);
      
      await client.replyMessage({
        replyToken: event.replyToken,
        messages: [
          {
            type: "text",
            text: "Pengeditan system prompt dibatalkan.",
            quoteToken: event.message.quoteToken,
          }
        ]
      });
      return true;
    }

    if (userMessage.toLowerCase() === "/default") {
      await db.delete(`customSystemPrompt_${userId}`);
      await db.delete(`syspromptActive_${sessionId}`);
      await db.delete(`syspromptOwner_${sessionId}`);
      
      await client.replyMessage({
        replyToken: event.replyToken,
        messages: [
          {
            type: "text",
            text: `System prompt untuk ${userName} telah direset ke default BaquaBot.`,
            quoteToken: event.message.quoteToken,
          }
        ]
      });
      return true;
    }

    // Save the new custom prompt
    await db.set(`customSystemPrompt_${userId}`, userMessage);
    await db.delete(`syspromptActive_${sessionId}`);
    await db.delete(`syspromptOwner_${sessionId}`);
    
    await client.replyMessage({
      replyToken: event.replyToken,
      messages: [
        {
          type: "text",
          text: `System prompt untuk ${userName} berhasil diubah!\n\nUntuk mengeditnya lagi gunakan !sysprompt atau !sysprompt /default untuk kembali ke default.`,
          quoteToken: event.message.quoteToken,
        }
      ]
    });

    return true;

  } catch (error) {
    console.error("Error handling sysprompt session:", error);
    
    // Clean up session on error
    await db.delete(`syspromptActive_${sessionId}`);
    await db.delete(`syspromptOwner_${sessionId}`);
    
    try {
      await client.replyMessage({
        replyToken: event.replyToken,
        messages: [
          {
            type: "text",
            text: "Terjadi kesalahan saat memproses system prompt. Sesi dibatalkan.",
            quoteToken: event.message.quoteToken,
          }
        ]
      });
    } catch (replyError) {
      console.error("Failed to send error reply:", replyError);
    }
    
    return true;
  }
};

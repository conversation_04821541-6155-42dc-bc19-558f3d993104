/**
 * sessionsTimeout.js
 * A universal manager for handling session timeouts.
 */

// This Map will store active setTimeout instances, with the session ID as the key.
const activeTimeouts = new Map();

// Universal timeout duration. Change it here to affect all commands.
const TIMEOUT_DURATION_MS = 3 * 60 * 1000; // 3 minutes

/**
 * Sets or resets a timeout for a given session ID.
 * When the timer expires, it executes the provided onTimeout callback function.
 * @param {string} sessionId - A unique identifier for the session (e.g., groupId).
 * @param {Function} onTimeout - The callback function to execute upon timeout.
 */
function setSessionTimeout(sessionId, onTimeout) {
    // If a timer already exists for this session, clear it before setting a new one.
    if (activeTimeouts.has(sessionId)) {
        clearTimeout(activeTimeouts.get(sessionId));
    }

    const timeoutId = setTimeout(() => {
        console.log(`Session [${sessionId}] timed out.`);
        
        // Execute the command-specific cleanup logic.
        onTimeout(); 
        
        // Remove the timeout from the map once it has fired.
        activeTimeouts.delete(sessionId);
    }, TIMEOUT_DURATION_MS);

    // Store the new timeoutID.
    activeTimeouts.set(sessionId, timeoutId);
}

/**
 * Clears an active timeout for a given session ID.
 * This should be called when a user interacts with the session.
 * @param {string} sessionId - The unique identifier for the session.
 */
function clearSessionTimeout(sessionId) {
    if (activeTimeouts.has(sessionId)) {
        clearTimeout(activeTimeouts.get(sessionId));
        activeTimeouts.delete(sessionId);
    }
}

module.exports = {
    setSessionTimeout,
    clearSessionTimeout,
};
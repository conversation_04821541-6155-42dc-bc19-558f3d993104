const winston = require('winston');

const { combine, timestamp, printf, colorize } = winston.format;

// Define the custom format for the logs
const logFormat = printf(({ level, message, timestamp }) => {
  return `${timestamp} ${level}: ${message}`;
});

// Create the logger instance
const logger = winston.createLogger({
  // Use the standard npm logging levels
  levels: winston.config.npm.levels,
  // Set the default level to the most permissive, transports will filter
  level: 'silly',
  // Define transports
  transports: [
    // Console transport for 'info' and lower
    new winston.transports.Console({
      level: 'info',
      format: combine(
        colorize(),
        timestamp({ format: 'YYYY-MM-DD HH:mm:ss' }),
        logFormat
      ),
    }),
    // File transport for 'error' logs
    new winston.transports.File({
      filename: 'logs/error.log',
      level: 'error',
      format: combine(
        timestamp({ format: 'YYYY-MM-DD HH:mm:ss' }),
        logFormat
      ),
    }),
    // File transport for all logs
    new winston.transports.File({
      filename: 'logs/combined.log',
      format: combine(
        timestamp({ format: 'YYYY-MM-DD HH:mm:ss' }),
        logFormat
      ),
    }),
  ],
});

module.exports = logger;
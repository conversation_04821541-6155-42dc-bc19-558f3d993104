const axios = require("axios");
const fs = require("fs");
const path = require("path");
const { generateApiUrl } = require("./urlSigner");

const analyzeImage = async (client, event, buffer, question) => {
  const timestamp = Date.now();
  const dirPath = path.join(__dirname, "../static/downloads");
  const filePath = path.join(dirPath, `${timestamp}.jpg`);

  if (!fs.existsSync(dirPath)) {
    fs.mkdirSync(dirPath, { recursive: true });
  }

  fs.writeFileSync(filePath, buffer);
  console.log(`Gambar berhasil disimpan di ${filePath}`);

  const imageUrl = generateApiUrl(`/downloads/${timestamp}.jpg`);

  try {
    const response = await axios.post(
      "http://127.0.0.1:5000/analyze_image",
      {
        image_url: imageUrl,
        question: question,
      }
    );

    const answer = response.data.answer;

    await client.replyMessage({
      replyToken: event.replyToken,
      messages: [
        {
          type: "text",
          text: `Hasil Analisis:\n\n${answer}`,
          quoteToken: event.message.quoteToken,
        },
      ],
    });
  } catch (error) {
    console.error("Error analyzing image:", error);
    await client.replyMessage({
      replyToken: event.replyToken,
      messages: [
        {
          type: "text",
          text: "Terjadi kesalahan saat menganalisis gambar.",
          quoteToken: event.message.quoteToken,
        },
      ],
    });
  } finally {
    fs.unlink(filePath, (err) => {
      if (err) console.error(`Failed to delete image file: ${filePath}`, err);
      else console.log(`Successfully deleted image file: ${filePath}`);
    });
  }
};

module.exports = { analyzeImage };
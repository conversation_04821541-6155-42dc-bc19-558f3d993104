# Flask AI Services API

![Python](https://img.shields.io/badge/python-3.9%2B-blue.svg)
![Flask](https://img.shields.io/badge/flask-2.x-black.svg)

This is a Flask-based API that provides several AI-powered services, including a chatbot, image generation and analysis, and an Akinator-like game.

## Prerequisites

- Python 3.9+
- pip
- virtualenv (recommended)

## Installation and Running

1.  **Clone the repository:**
    ```bash
    git clone <your-repo-url>
    cd <your-repo-directory>
    ```

2.  **Create and activate a virtual environment:**
    ```bash
    python -m venv venv
    source venv/bin/activate  # On Windows, use `venv\Scripts\activate`
    ```

3.  **Install the dependencies:**
    ```bash
    pip install -r requirements.txt
    ```
    
4.  **Create a `.env` file** in the root directory and add your Google API key:
    ```
    GOOGLE_API_KEY=your_google_api_key
    ```

5.  **Run the application:**
    ```bash
    python run.py
    ```
    The server will start on `http://127.0.0.1:5000`.

## API Endpoints

### Chat

#### `POST /chat`

Engages in a conversation with a chatbot.

-   **Request Body:**
    ```json
    {
        "messages": [
            {"role": "user", "content": "Hello, who are you?"}
        ]
    }
    ```
-   **Success Response (200):**
    ```json
    "I am a large language model, trained by Google."
    ```
-   **Error Response (400):**
    ```json
    {
        "error": "Messages are required"
    }
    ```

### Image

#### `POST /image`

Generates an image based on a prompt.

-   **Request Body:**
    ```json
    {
        "prompt": "A futuristic city with flying cars"
    }
    ```
-   **Success Response (200):**
    ```json
    {
        "url": "http://example.com/path/to/image.png"
    }
    ```
-   **Error Response (400):**
    ```json
    {
        "error": "Prompt is required"
    }
    ```

#### `POST /analyze_image`

Analyzes an image and answers a question about it.

-   **Request Body:**
    ```json
    {
        "image_url": "http://example.com/path/to/image.png",
        "question": "What is the main color in this image?"
    }
    ```
-   **Success Response (200):**
    ```json
    {
        "answer": "The main color in the image is blue."
    }
    ```
-   **Error Response (400):**
    ```json
    {
        "error": "Image URL is required"
    }
    ```

### Akinator

This set of endpoints allows you to play a game of Akinator.

#### `POST /akinator/start`

Starts a new Akinator game session.

-   **Request Body:** (empty)
-   **Success Response (200):**
    ```json
    {
        "session_id": "some-uuid",
        "question": "Is your character a man?"
    }
    ```

#### `POST /akinator/answer`

Submits an answer to the current question.

-   **Request Body:**
    ```json
    {
        "session_id": "some-uuid",
        "answer": "yes"
    }
    ```
-   **Success Response (200 - game continues):**
    ```json
    {
        "question": "Is your character a real person?",
        "step": 1,
        "finished": false
    }
    ```
-   **Success Response (200 - game finished):**
    ```json
    {
        "guess": {
            "name_proposition": "Albert Einstein",
            "description_proposition": "Physicist",
            "photo": "http://example.com/path/to/photo.jpg"
        },
        "finished": true
    }
    ```

#### `POST /akinator/back`

Goes back to the previous question.

-   **Request Body:**
    ```json
    {
        "session_id": "some-uuid"
    }
    ```
-   **Success Response (200):**
    ```json
    {
        "question": "Is your character a man?",
        "step": 0
    }
    ```

#### `POST /akinator/end`

Ends the current game session.

-   **Request Body:**
    ```json
    {
        "session_id": "some-uuid"
    }
    ```
-   **Success Response (200):**
    ```json
    {
        "message": "Session ended"
    }
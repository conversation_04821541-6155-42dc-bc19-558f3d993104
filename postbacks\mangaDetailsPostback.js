const axios = require("axios");
const utils = require("../utils/utils")
module.exports = {
  postbackData: "mangaData=", // Prefix for dynamic sticker postbacks
  cooldown: 10,
  handler: async (client, blobClient, event, id) => {
    
    const mangaFullById = await axios(`https://api.jikan.moe/v4/manga/${id}/full`)
    
    const replyText =
      `Title: ${mangaFullById.data.data.title || "Unknown"}\n` +
      `Japanese: ${mangaFullById.data.data.title_japanese || "Unknown"}\n` +
      `Synonyms: ${mangaFullById.data.data.title_synonyms.length > 0 ? mangaFullById.data.data.title_synonyms.join(", ") : "None"}\n` +
      `Chapters: ${mangaFullById.data.data.chapters || "Unknown"}\n` +
      `Status: ${mangaFullById.data.data.status || "Unknown"}\n` +
      `Rating: ${mangaFullById.data.data.score || "N/A"}\n` +
      `Volumes: ${mangaFullById.data.data.volumes || "Unknown"}\n` +
      `Published: ${mangaFullById.data.data.published.string || "Unknown"}\n` +
      `Type: ${mangaFullById.data.data.type || "Unknown"}\n` +
      `Synopsis: ${utils.TruncateText(mangaFullById.data.data.synopsis || "No synopsis available.", 2000)}\n` +
      `Genres: ${mangaFullById.data.data.genres.length > 0 ? mangaFullById.data.data.genres.map((genre) => genre.name).join(", ") : "Unknown"}\n` +
      `Authors: <AUTHORS>
      `Serialization: ${mangaFullById.data.data.serializations.length > 0 ? mangaFullById.data.data.serializations.map((serial) => serial.name).join(", ") : "Unknown"}\n` +
      `More: ${mangaFullById.data.data.url || "No additional information"}`;

    client.replyMessage({
      replyToken: event.replyToken,
      messages: [
        {
          type: "text",
          text: replyText,
          
        },
      ],
    });
  },
};

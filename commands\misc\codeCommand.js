module.exports = {
  command: "code",
  aliases: [],
  category: "misc",
  description: "Menjalankan kode JavaScript (Node.js) melalui bot",
  requiresPrefix: true,
  includes: false,
  handler: async (client, blobClient, event, args) => {
    // Gabungkan semua argumen menjadi satu string kode
    const code = args.join(" ");

    if (!code) {
      return client.replyMessage({
        replyToken: event.replyToken,
        messages: [
          {
            type: "text",
            text: "⚠️ Harap masukkan kode dan syntax JavaScript untuk dieksekusi.",
            quoteToken: event.message.quoteToken,
          },
        ],
      });
    }

    // Cegah akses ke variabel berbahaya seperti "process", "require", dll.
    const forbiddenKeywords = [
      "process",
      "require",
      "global",
      "module",
      "exports",
    ];

    // Periksa apakah kode mengandung kata-kata yang dilarang
    if (forbiddenKeywords.some((keyword) => code.includes(keyword))) {
      return client.replyMessage({
        replyToken: event.replyToken,
        messages: [
          {
            type: "text",
            text: "⚠️ Kode mengandung kata-kata terlarang.",
            quoteToken: event.message.quoteToken,
          },
        ],
      });
    }

    try {
      // Gunakan IIFE untuk mencegah akses variabel luar
      const result = eval(code);

      // Kirim hasil eksekusi
      await client.replyMessage({
        replyToken: event.replyToken,
        messages: [
          {
            type: "text",
            text: `✅ Output:\n${result}`,
            quoteToken: event.message.quoteToken,
          },
        ],
      });
    } catch (error) {
      // Tangkap error
      await client.replyMessage({
        replyToken: event.replyToken,
        messages: [
          {
            type: "text",
            text: `❌ Error:\n${error.message}`,
            quoteToken: event.message.quoteToken,
          },
        ],
      });
    }
  },
};

const axios = require("axios");

module.exports = {
  command: "steam",
  aliases: [],
  description: "Cari game di Steam Store berdasarkan judul.",
  category: "utility",
  requiresPrefix: true,
  includes: false,
  handler: async (client, blobClient, event, args) => {
    const query = args.join(" ");
    if (!query) {
      return client.replyMessage({
        replyToken: event.replyToken,
        messages: [
          {
            type: "text",
            text: "Harap masukkan nama game yang ingin dicari, contoh: !steam dota",
          },
        ],
      });
    }

    try {
      // Fetch game data from Steam API
      const response = await axios.get(
        `https://store.steampowered.com/api/storesearch/?cc=id&term=${encodeURIComponent(
          query
        )}`
      );
      const games = response.data.items;

      if (games.length === 0) {
        return client.replyMessage({
          replyToken: event.replyToken,
          messages: [
            {
              type: "text",
              text: `<PERSON><PERSON><PERSON> ditemukan hasil untuk "${query}".`,
            },
          ],
        });
      }

      // Create an array to hold the Flex Messages
      const flexMessages = games.slice(0, 10).map((game) => {
        

        const originalPrice = game.price ? Number(game.price.initial / 100) : 0;
        const finalPrice = game.price ? Number(game.price.final / 100) : 0;

        const originalPriceText =
          originalPrice > 0
            ? `Rp ${originalPrice.toLocaleString("id-ID")}`
            : "Gratis";

        const finalPriceText =
          finalPrice < originalPrice
            ? `Rp ${finalPrice.toLocaleString("id-ID")}`
            : originalPriceText;

        let discountPercent = "";
        if (finalPrice < originalPrice) {
          const discount = Math.round(
            ((originalPrice - finalPrice) / originalPrice) * 100
          );
          discountPercent = ` (${discount}%)`;
        }

        return {
          type: "bubble",
          size: "hecto",
          hero: {
            type: "image",
            url: `https://to-jpg.vercel.app/convert?url=${game.tiny_image}&format=jpg`,
            size: "full",
            aspectRatio: "2:1",
            aspectMode: "cover",
            action: {
              type: "uri",
              uri: `https://store.steampowered.com/app/${game.id}`,
            },
          },
          body: {
            type: "box",
            layout: "vertical",
            contents: [
              {
                type: "text",
                text: game.name,
                weight: "bold",
                size: "md",
                wrap: true,
              },
              {
                type: "box",
                layout: "vertical",
                margin: "md",
                contents: [
                  {
                    type: "text",
                    text: `${originalPriceText}`,
                    size: "sm",
                    color: "#555555",
                    decoration: finalPrice < originalPrice ? "line-through" : "none",
                  },
                  finalPrice < originalPrice
                    ? {
                        type: "text",
                        text: `${finalPriceText}${discountPercent}`,
                        size: "sm",
                        color: "#FF0000",
                        weight: "bold",
                        margin: "sm",
                        wrap: true,
                      }
                    : null,
                ].filter(Boolean),
              },
            ],
          },
          footer: {
            type: "box",
            layout: "vertical",
            spacing: "sm",
            contents: [
              {
                type: "button",
                style: "primary",
                height: "sm",
                color: "#4DA8DA",
                action: {
                  type: "uri",
                  label: "Open on Steam",
                  uri: `https://store.steampowered.com/app/${game.id}`,
                },
              },
              {
                type: "button",
                style: "primary",
                height: "sm",
                color: "#FF6B9D", // pink
                action: {
                  type: "postback",
                  label: "See Details",
                  data: `steamData=${game.id}`,
                  displayText: `See Details ${game.name}`,
                },
              },
            ],
          },
        };
      });

      // Create the Flex Message payload
      const flexMessage = {
        type: "carousel",
        contents: flexMessages,
      };

      // Send Flex Message
      await client.replyMessage({
        replyToken: event.replyToken,
        messages: [
          {
            type: "flex",
            altText: `Hasil pencarian Steam untuk: ${query}`,
            contents: flexMessage,
          },
        ],
      });
    } catch (error) {
      console.error("Error fetching data from Steam API:", error);
      client.replyMessage({
        replyToken: event.replyToken,
        messages: [
          {
            type: "text",
            text: "Terjadi kesalahan saat mencari game di Steam. Silakan coba lagi nanti.",
            quoteToken: event.message.quoteToken
          },
        ],
      });
    }
  },
};

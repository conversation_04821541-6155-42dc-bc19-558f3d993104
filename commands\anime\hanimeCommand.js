const { HAnimeAPI } = require("hanime");
const api = new HAnimeAPI();

module.exports = {
  command: "hanime",
  aliases: ["htv"],
  category: "anime",
  description: "Search hentai di hanime",
  requiresPrefix: true,
  includes: false,
  handler: async (client, blobClient, event, args) => {
    if (args.length === 0) {
      return client.replyMessage({
        replyToken: event.replyToken,
        messages: [
          {
            type: "text",
            text: "Please provide a search query",
            quoteToken: event.message.quoteToken,
          },
        ],
      });
    }

    const results = await api.search(args.join(" "));
    if (!results) {
      return client.replyMessage({
        replyToken: event.replyToken,
        messages: [
          {
            type: "text",
            text: "No results found",
            quoteToken: event.message.quoteToken,
          },
        ],
      });
    }

    const messageContent = {
      type: "flex",
      altText: "Gogoanime search results",
      contents: {
        type: "carousel",
        contents: results.videos.slice(0, 12).map((anime) => {
          return {
            type: "bubble",
            size: "hecto",
            hero: {
              type: "image",
              url: anime.cover_url,
              size: "full",
              aspectRatio: "2:3",
              aspectMode: "cover",
            },
            body: {
              type: "box",
              layout: "vertical",
              contents: [
                {
                  type: "text",
                  text: anime.name,
                  wrap: true,
                  weight: "bold",
                  size: "md",
                },
                {
                  type: "text",
                  text: anime.is_censored ? "Censored" : "Uncensored",
                  color: anime.is_censored ? "#ff0000" : "#008000",
                  wrap: true,
                },
                {
                  type: "text",
                  text: anime.brand,
                  size: "xs",
                  color: "#aaaaaa",
                  wrap: true,
                },
              ],
            },
            footer: {
              type: "box",
              layout: "vertical",
              spacing: "sm",
              contents: [
                {
                  type: "button",
                  style: "primary",
                  height: "sm",
                  action: {
                    type: "uri",
                    label: "Open Hanime",
                    uri: `https://hanime.tv/videos/hentai/${anime.slug}`,
                  },
                  color: "#4DA8DA", // blue
                },
                {
                  type: "button",
                  style: "primary",
                  height: "sm",
                  action: {
                    type: "postback",
                    label: "See details",
                    data: `hanimeData=${anime.name}`,
                    displayText: `See detail ${anime.name}`,
                  },
                  color: "#FF6B9D", // pink
                },
              ],
            },
          };
        }),
      },
    };

    client.replyMessage({
      replyToken: event.replyToken,
      messages: [messageContent],
    });
  },
};

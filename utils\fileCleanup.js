const fs = require("fs");
const path = require("path");

/**
 * Clean up old files from the downloads directory
 * @param {number} maxAgeHours - Maximum age of files in hours (default: 24)
 */
function cleanupOldFiles(maxAgeHours = 24) {
  const downloadsDir = path.join(__dirname, "../static/downloads");
  
  if (!fs.existsSync(downloadsDir)) {
    console.log("Downloads directory does not exist, skipping cleanup");
    return;
  }

  const now = Date.now();
  const maxAge = maxAgeHours * 60 * 60 * 1000; // Convert hours to milliseconds
  let deletedCount = 0;
  let totalSize = 0;

  try {
    const files = fs.readdirSync(downloadsDir);
    
    for (const file of files) {
      const filePath = path.join(downloadsDir, file);
      const stats = fs.statSync(filePath);
      
      // Check if file is older than maxAge
      if (now - stats.mtime.getTime() > maxAge) {
        try {
          totalSize += stats.size;
          fs.unlinkSync(filePath);
          deletedCount++;
          console.log(`Deleted old file: ${file}`);
        } catch (error) {
          console.error(`Error deleting file ${file}:`, error.message);
        }
      }
    }
    
    if (deletedCount > 0) {
      console.log(`Cleanup completed: ${deletedCount} files deleted, ${(totalSize / 1024 / 1024).toFixed(2)} MB freed`);
    } else {
      console.log("No old files found for cleanup");
    }
  } catch (error) {
    console.error("Error during file cleanup:", error.message);
  }
}

/**
 * Get directory size and file count
 * @param {string} dirPath - Directory path
 * @returns {object} - Object with size and count
 */
function getDirectoryInfo(dirPath) {
  if (!fs.existsSync(dirPath)) {
    return { size: 0, count: 0 };
  }

  let totalSize = 0;
  let fileCount = 0;

  try {
    const files = fs.readdirSync(dirPath);
    
    for (const file of files) {
      const filePath = path.join(dirPath, file);
      const stats = fs.statSync(filePath);
      
      if (stats.isFile()) {
        totalSize += stats.size;
        fileCount++;
      }
    }
  } catch (error) {
    console.error("Error getting directory info:", error.message);
  }

  return {
    size: totalSize,
    count: fileCount,
    sizeFormatted: `${(totalSize / 1024 / 1024).toFixed(2)} MB`
  };
}

/**
 * Start automatic cleanup interval
 * @param {number} intervalHours - Cleanup interval in hours (default: 6)
 * @param {number} maxAgeHours - Maximum age of files in hours (default: 24)
 */
function startAutoCleanup(intervalHours = 6, maxAgeHours = 24) {
  const intervalMs = intervalHours * 60 * 60 * 1000;
  
  console.log(`Starting auto cleanup: every ${intervalHours} hours, deleting files older than ${maxAgeHours} hours`);
  
  // Run cleanup immediately
  cleanupOldFiles(maxAgeHours);
  
  // Set up interval
  setInterval(() => {
    console.log("Running scheduled file cleanup...");
    cleanupOldFiles(maxAgeHours);
  }, intervalMs);
}

module.exports = {
  cleanupOldFiles,
  getDirectoryInfo,
  startAutoCleanup
};

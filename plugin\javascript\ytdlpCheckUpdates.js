const { spawn } = require("child_process");
const axios = require("axios");
const fs = require("fs");
const path = require("path");
const winston = require("winston");

// Logger for output
const logger = winston.createLogger({
  level: "info",
  format: winston.format.combine(
    winston.format.colorize(),
    winston.format.timestamp({ format: "YYYY-MM-DD HH:mm:ss" }),
    winston.format.printf(
      ({ level, message, timestamp }) => `[${timestamp}] ${level}: ${message}`
    )
  ),
  transports: [new winston.transports.Console()],
});

const ytDlpPath = path.join(__dirname, "../../bin/yt-dlp.exe");

// Function to get the local yt-dlp version
async function getLocalVersion() {
  return new Promise((resolve, reject) => {
    if (!fs.existsSync(ytDlpPath)) {
      logger.warn("Local yt-dlp.exe not found. Assuming update is needed.");
      return resolve("0"); // Resolve with a version that guarantees an update
    }
    const ytDlp = spawn(ytDlpPath, ["--version"], {
      windowsHide: true,
    });

    let version = "";
    ytDlp.stdout.on("data", (data) => {
      version += data.toString();
    });

    ytDlp.stderr.on("data", (data) => {
      logger.error(`yt-dlp stderr: ${data.toString().trim()}`);
    });

    ytDlp.on("close", (code) => {
      if (code === 0) {
        resolve(version.trim());
      } else {
        // If getting version fails, it might be a corrupted file. Better to update.
        logger.error(`Failed to get yt-dlp version. Exit code: ${code}. Attempting update.`);
        resolve("0");
      }
    });
  });
}

// Function to get the latest nightly version tag from the correct GitHub repository
async function getLatestNightlyVersion() {
  try {
    const apiUrl = "https://api.github.com/repos/yt-dlp/yt-dlp-nightly-builds/releases/latest";
    const response = await axios.get(apiUrl);
    return response.data.tag_name; // This will be the full version, e.g., "2025.06.08.232939"
  } catch (error) {
    logger.error("Error fetching latest nightly version:", error.message);
    throw error;
  }
}

// Function to update yt-dlp if needed
async function updateYtDlp() {
  try {
    const localVersion = await getLocalVersion();
    const latestNightlyVersion = await getLatestNightlyVersion();

    logger.info(`Local yt-dlp version:     ${localVersion}`);
    logger.info(`Latest nightly version:   ${latestNightlyVersion}`);

    if (localVersion === latestNightlyVersion) {
      logger.info("yt-dlp is already up-to-date with the latest nightly build!");
      return;
    }

    logger.info("Updating yt-dlp to the latest nightly build...");

    // Dynamically construct the download URL based on the latest version tag
    const downloadUrl = `https://github.com/yt-dlp/yt-dlp-nightly-builds/releases/download/${latestNightlyVersion}/yt-dlp.exe`;
    logger.info(`Downloading from: ${downloadUrl}`);
    
    const tempFilePath = path.join(__dirname, "../../bin/yt-dlp.temp.exe");
    const curl = spawn("curl", ["-L", downloadUrl, "-o", tempFilePath], {
      windowsHide: true,
    });

    curl.on("close", (code) => {
      if (code === 0) {
        // Ensure temp file downloaded correctly
        if (!fs.existsSync(tempFilePath) || fs.statSync(tempFilePath).size === 0) {
            logger.error("Downloaded file is empty or missing. Aborting update.");
            return;
        }

        fs.unlink(ytDlpPath, (err) => {
          if (err && err.code !== 'ENOENT') { // Ignore error if old file doesn't exist
            logger.error("Error removing old yt-dlp file:", err);
            return;
          }
          fs.rename(tempFilePath, ytDlpPath, (err) => {
            if (err) {
              logger.error("Error renaming new yt-dlp file:", err);
              return;
            }
            logger.info(`yt-dlp updated successfully to version ${latestNightlyVersion}!`);
          });
        });
      } else {
        logger.error(`Failed to download yt-dlp nightly. curl exit code: ${code}`);
      }
    });
  } catch (error) {
    logger.error("Error during the yt-dlp update process:", error.message);
  }
}

module.exports = {
  updateYtDlp,
};
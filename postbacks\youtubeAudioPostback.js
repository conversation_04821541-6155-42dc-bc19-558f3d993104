const { spawn } = require("child_process"); // Import spawn from child_process
const fs = require("fs");
const path = require("path");
const { formatDuration } = require("../utils/utils");
const { generateSecureUrl } = require("../utils/urlSigner");

const ytDlpBinary = "./bin/yt-dlp.exe"; // Define the path to your yt-dlp executable

module.exports = {
  postbackData: "audioYoutubeData=", // Prefix for dynamic sticker postbacks
  cooldown: 10,
  handler: async (client, blobClient, event, id) => {
    const videoUrl = id; // Use dynamic URL from args if provided
    const maxFileSize = 200 * 1024 * 1024; // 200MB

    // Helper function to get video metadata using yt-dlp with --dump-json
    const getVideoInfo = (url) => {
      return new Promise((resolve, reject) => {
        const infoProcess = spawn(ytDlpBinary, [url, "--dump-json"],
          { windowsHide: true }
        );
        let output = "";
        let errorOutput = "";

        infoProcess.stdout.on("data", (data) => {
          output += data.toString();
        });

        infoProcess.stderr.on("data", (data) => {
          errorOutput += data.toString();
        });

        infoProcess.on("close", (code) => {
          if (code !== 0) {
            reject(new Error(`yt-dlp failed to fetch video info. Stderr: ${errorOutput}`));
          } else {
            try {
              const info = JSON.parse(output);
              resolve(info);
            } catch (err) {
              reject(err);
            }
          }
        });

        infoProcess.on("error", (err) => {
          reject(err);
        });
      });
    };

    let metadata;
    try {
      metadata = await getVideoInfo(videoUrl);

      if (metadata.is_live) {
        return client.replyMessage({
          replyToken: event.replyToken,
          messages: [{
            type: "text",
            text: "You cannot download live videos.",
          }, ],
        });
      }

      if (metadata.availability === "private") {
        return client.replyMessage({
          replyToken: event.replyToken,
          messages: [{
            type: "text",
            text: "This video is private and cannot be downloaded.",
          }, ],
        });
      }
    } catch (error) {
      console.error("Failed to fetch video metadata:", error);
      return client.replyMessage({
        replyToken: event.replyToken,
        messages: [{
          type: "text",
          text: "Failed to fetch video metadata. Please try again later.",
        }, ],
      });
    }

    let user;
    if (event.source.type === "group") {
      user = await client.getGroupMemberProfile(
        event.source.groupId,
        event.source.userId
      );
    } else {
      user = await client.getProfile(event.source.userId);
    }
    const userProfile = user.displayName;

    const downloadsDir = "./static/downloads";
    if (!fs.existsSync(downloadsDir)) {
      fs.mkdirSync(downloadsDir, { recursive: true });
    }

    const timestamp = Date.now();
    const outputFilePath = path.join(downloadsDir, `${timestamp}.mp3`);

    const downloadProcess = spawn(ytDlpBinary, [
      videoUrl, // The video URL
      "-x", // Extract audio
      "--audio-format", "mp3", // Specify audio format
      "-o", outputFilePath, // Define the output file path
    ],
      { windowsHide: true }
    );

    downloadProcess.stdout.on('data', (data) => {
        // You can log progress here if needed
        console.log(`yt-dlp stdout: ${data}`);
    });

    downloadProcess.stderr.on('data', (data) => {
        // Log errors from yt-dlp
        console.error(`yt-dlp stderr: ${data}`);
    });

    downloadProcess.on("error", (error) => {
      console.error("Failed to start download process:", error);
      // Handle process start error if necessary
    });

    downloadProcess.on("close", (code) => {
      if (code !== 0) {
        console.error(`yt-dlp process exited with code ${code}`);
        // Optionally send a failure message to the user
        return client.replyMessage({
          replyToken: event.replyToken,
          messages: [{
            type: "text",
            text: "Failed to download the audio.",
          }, ],
        });
      }

      // Check if file was created and its size
      if (!fs.existsSync(outputFilePath)) {
          console.error("Output file not found after download.");
          return;
      }

      if (fs.statSync(outputFilePath).size > maxFileSize) {
        fs.unlink(outputFilePath, (err) => {
          if (err) {
            console.error("Error deleting oversized file:", err);
          } else {
            console.log("Oversized file deleted successfully");
          }
        });
        return client.replyMessage({
          replyToken: event.replyToken,
          messages: [{
            type: "text",
            text: "Video size exceeds the maximum allowed size of 200 MB.",
          }, ],
        });
      }

      const audioUrl = generateSecureUrl(`/downloads/${timestamp}.mp3`, 120);

      const messageContent = [{
        type: "audio",
        originalContentUrl: audioUrl,
        duration: Number(metadata.duration * 1000),
      }, {
        type: "text",
        text: `🔉${metadata.title}\n${formatDuration(metadata.duration)}\n\nRequested by ${userProfile}`,
      }, ];

      client.replyMessage({
        replyToken: event.replyToken,
        messages: messageContent,
      });
    });
  },
};
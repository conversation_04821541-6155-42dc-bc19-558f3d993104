version: "3.9"
services:
  vajrabot:
    build: .
    ports:
      - "3000:3000"
    depends_on:
      - gluetun
    networks:
      - vpn
    environment:
      - NODE_ENV=production # Or development if needed

  gluetun:
    image: qmcgaw/gluetun
    cap_add:
      - NET_ADMIN
    ports:
      - "8888:8888/tcp" # HTTP proxy
      - "9999:9999/tcp" # Shadowsocks
      - "8388:8388/tcp" # Shadowsocks AEAD
      - "443:443/tcp" # HTTPS
      - "443:443/udp" # HTTPS
      - "80:80/tcp"   # HTTP
    environment:
      - VPN_SERVICE=wireguard
      - VPN_ENDPOINT=your_vpn_endpoint # Replace with your VPN endpoint
      - VPN_PRIVATE_KEY=your_vpn_private_key # Replace with your VPN private key
      - PASSWORD=password # Optional: HTTP proxy password
      - HTTPPROXY=on # Optional: Enable HTTP proxy
      - SHADOWSOCKS=on # Optional: Enable Shadowsocks
      - SHADOWSOCKS_AEAD=on # Optional: Enable Shadowsocks AEAD
    networks:
      - vpn

networks:
  vpn:
    name: vpn
    driver: bridge
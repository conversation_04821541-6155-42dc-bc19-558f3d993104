const os = require("os");

const { version: nodeVersion } = process;
const { version: botVersion } = require("../../package.json");
const { version: lineSdkVersion } = require("@line/bot-sdk/package.json");

module.exports = {
  command: "botinfo",
  aliases: ["bot", "info"],
  category: "misc",
  description: "Menampilkan informasi bot",
  requiresPrefix: true,
  includes: false,
  handler: async (client, blobClient, event) => {
    try {
      // Dapatkan informasi bot dari LINE
      const botInfo = await client.getBotInfo();

      // Hitung jumlah total commands
      const totalCommands = client.commands.size;

      // Dapatkan informasi sistem
      const botName = botInfo.displayName;
      const botPictureUrl =
        botInfo.pictureUrl || "https://via.placeholder.com/80x80?text=No+Image";
      const osType = os.type();
      const osVersion = os.release();
      const osPlatform = os.platform();
      const cpuArch = os.arch();
      const totalMemory = (os.totalmem() / 1024 / 1024 / 1024).toFixed(2); // Dalam GB
      const freeMemory = (os.freemem() / 1024 / 1024 / 1024).toFixed(2); // Dalam GB
      console.log(botInfo.pictureUrl)
      // Hitung uptime bot menggunakan moment.js
      // Uptime calculation without Moment.js
      const uptimeSeconds = Math.floor(process.uptime());
      const hours = Math.floor(uptimeSeconds / 3600);
      const minutes = Math.floor((uptimeSeconds % 3600) / 60);
      const seconds = uptimeSeconds % 60;

      // Format uptime
      let uptimeFormatted = "";
      if (hours > 0) uptimeFormatted += `${hours}j `;  // j = jam
      if (minutes > 0) uptimeFormatted += `${minutes}m `;
      uptimeFormatted += `${seconds}d`;  // d = detik

      // Flex Message
      const flexMessage = {
        type: "flex",
        altText: "Informasi Bot",
        contents: {
          type: "bubble",
          size: "hecto",
          body: {
            type: "box",
            layout: "vertical",
            contents: [
              {
                type: "box",
                layout: "horizontal",
                contents: [
                  {
                    type: "box",
                    layout: "vertical",
                    contents: [
                      {
                        type: "box",
                        layout: "vertical",
                        contents: [
                          {
                            type: "image",
                            url: botPictureUrl,
                          },
                        ],
                      },
                    ],
                    cornerRadius: "220px",
                    maxWidth: "50px",
                    maxHeight: "50px",
                  },
                  {
                    type: "box",
                    layout: "vertical",
                    contents: [
                      {
                        type: "text",
                        text: botName,
                        weight: "bold",
                      },
                      {
                        type: "text",
                        text: "Bot Information",
                        size: "xxs",
                        color: "#555555",
                        wrap: true,
                      },
                    ],
                    paddingTop: "md",
                    margin: "lg",
                  },
                ],
              },
              {
                type: "separator",
                margin: "md",
              },
              {
                type: "box",
                layout: "vertical",
                margin: "lg",
                spacing: "xs",
                contents: [
                  {
                    type: "box",
                    layout: "horizontal",
                    contents: [
                      {
                        type: "text",
                        text: "Total Commands",
                        size: "xs",
                        color: "#555555",
                      },
                      {
                        type: "text",
                        text: `${totalCommands}`,
                        size: "xs",
                        color: "#111111",
                        align: "end",
                      },
                    ],
                  },
                  {
                    type: "box",
                    layout: "horizontal",
                    contents: [
                      {
                        type: "text",
                        text: "Bot Version",
                        size: "xs",
                        color: "#555555",
                      },
                      {
                        type: "text",
                        text: botVersion,
                        size: "xs",
                        color: "#111111",
                        align: "end",
                      },
                    ],
                  },
                  {
                    type: "box",
                    layout: "horizontal",
                    contents: [
                      {
                        type: "text",
                        text: "Node.js Version",
                        size: "xs",
                        color: "#555555",
                      },
                      {
                        type: "text",
                        text: nodeVersion,
                        size: "xs",
                        color: "#111111",
                        align: "end",
                      },
                    ],
                  },
                  {
                    type: "box",
                    layout: "horizontal",
                    contents: [
                      {
                        type: "text",
                        text: "@line/bot-sdk",
                        size: "xs",
                        color: "#555555",
                      },
                      {
                        type: "text",
                        text: lineSdkVersion,
                        size: "xs",
                        color: "#111111",
                        align: "end",
                      },
                    ],
                  },
                  {
                    type: "box",
                    layout: "horizontal",
                    contents: [
                      {
                        type: "text",
                        text: "OS",
                        size: "xs",
                        color: "#555555",
                      },
                      {
                        type: "text",
                        text: `${osType} ${osVersion}`,
                        size: "xs",
                        color: "#111111",
                        align: "end",
                      },
                    ],
                  },
                  {
                    type: "box",
                    layout: "horizontal",
                    contents: [
                      {
                        type: "text",
                        text: "Platform",
                        size: "xs",
                        color: "#555555",
                      },
                      {
                        type: "text",
                        text: osPlatform,
                        size: "xs",
                        color: "#111111",
                        align: "end",
                      },
                    ],
                  },
                  {
                    type: "box",
                    layout: "horizontal",
                    contents: [
                      {
                        type: "text",
                        text: "CPU Arch",
                        size: "xs",
                        color: "#555555",
                      },
                      {
                        type: "text",
                        text: cpuArch,
                        size: "xs",
                        color: "#111111",
                        align: "end",
                      },
                    ],
                  },
                  {
                    type: "box",
                    layout: "horizontal",
                    contents: [
                      {
                        type: "text",
                        text: "Total Memory",
                        size: "xs",
                        color: "#555555",
                      },
                      {
                        type: "text",
                        text: `${totalMemory} GB`,
                        size: "xs",
                        color: "#111111",
                        align: "end",
                      },
                    ],
                  },
                  {
                    type: "box",
                    layout: "horizontal",
                    contents: [
                      {
                        type: "text",
                        text: "Free Memory",
                        size: "xs",
                        color: "#555555",
                      },
                      {
                        type: "text",
                        text: `${freeMemory} GB`,
                        size: "xs",
                        color: "#111111",
                        align: "end",
                      },
                    ],
                  },
                  {
                    type: "box",
                    layout: "horizontal",
                    contents: [
                      {
                        type: "text",
                        text: "Uptime",
                        size: "xs",
                        color: "#555555",
                      },
                      {
                        type: "text",
                        text: `${uptimeFormatted}`,
                        size: "xs",
                        color: "#111111",
                        align: "end",
                      },
                    ],
                  },
                ],
              },
            ],
          },
        },
      };

      // Kirim Flex Message
      client.replyMessage({
        replyToken: event.replyToken,
        messages: [flexMessage],
      });
    } catch (error) {
      console.error("Error fetching bot info:", error);
      client.replyMessage({
        replyToken: event.replyToken,
        messages: [
          {
            type: "text",
            text: "❌ Terjadi kesalahan saat mengambil informasi bot",
            quoteToken: event.quoteToken,
          },
        ],
      });
    }
  },
};

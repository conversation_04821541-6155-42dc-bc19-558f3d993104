const axios = require('axios');
const wanakana = require('wanakana');
const { QuickDB } = require('quick.db');
const db = new QuickDB();

module.exports = {
  command: 'jisho',
  aliases: ['jpdict'],
  category: 'utility',
  description: 'Search a Japanese word using Jisho.org!',
  requiresPrefix: true,
  includes: false,
  handler: async (client, blobClient, event, args) => {
    const query = args.join(' ');
    if (!query) {
      return client.replyMessage({
        replyToken: event.replyToken,
        messages: [{ type: 'text', text: '❌ Please provide a word to search.' }]
      });
    }

    let res;
    try {
      res = await axios.get(`https://jisho.org/api/v1/search/words?keyword=${encodeURIComponent(query)}`, {
        headers: {
          'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/58.0.3029.110 Safari/537.3'
        }
      });
    } catch (error) {
      console.error(error);
      return client.replyMessage({
        replyToken: event.replyToken,
        messages: [{ type: 'text', text: '❌ Failed to fetch data from Jisho.org.' }]
      });
    }

    const data = res.data.data;
    if (!data.length) {
      return client.replyMessage({
        replyToken: event.replyToken,
        messages: [{ type: 'text', text: '❌ No results found for that word.' }]
      });
    }

    // Send a maximum of 5 messages with the results
    const messages = data.slice(0, 5).map((item, index) => {
      const text = generateJishoText(item, index + 1, data.length);
      return { type: 'text', text };
    });

    await client.replyMessage({
      replyToken: event.replyToken,
      messages: messages
    });
  }
};

function generateJishoText(item, current, total) {
  const japanese = item.japanese[0];
  const word = japanese.word || japanese.reading;
  const reading = japanese.reading;
  const romaji = wanakana.toRomaji(reading);

  const meanings = item.senses.slice(0, 3).map((sense, idx) => {
    const english = sense.english_definitions.join(', ');
    const part = sense.parts_of_speech.join(', ') || 'No part of speech';
    return `${idx + 1}. ${english} (${part})`;
  });

  const jlpt = item.jlpt.length ? item.jlpt.join(', ').toUpperCase() : 'N/A';
  const isCommon = item.is_common ? '✅ Yes' : '❌ No';
  const tags = item.senses[0]?.tags?.join(', ') || 'None';

  return `📘 ${word} (${romaji})\n📖 Reading: ${reading}\n\n` +
         `🔤 Meanings:\n${meanings.join('\n')}\n\n` +
         `🎓 JLPT Level: ${jlpt}\n📌 Common Word: ${isCommon}\n🏷️ Usage Tags: ${tags}`
}

const { evaluate } = require("mathjs");

module.exports = {
  command: "math",
  aliases: ["calc", "eval"],
  category: "utility",
  description: "Menghitung aja perlu bot, skill issue",
  requiresPrefix: true,
  includes: false,
  handler: async (client, blobClient, event, args) => {
    // <PERSON>ka tidak ada input, beri pesan kesalahan
    if (!args.length) {
      return client.replyMessage({
        replyToken: event.replyToken,
        messages: [
          {
            type: "text",
            text: "⚠️ Masukkan ekspresi matematika yang ingin dihitung, contoh: !math 5 + 3 * (2 - 1)",
            quoteToken: event.message.quoteToken,
          },
        ],
      });
    }

    const expression = args.join(" ");

    try {
      // Evaluasi ekspresi matematika menggunakan Math.js
      const result = evaluate(expression);

      // <PERSON><PERSON> hasil perhitungan
      client.replyMessage({
        replyToken: event.replyToken,
        messages: [
          {
            type: "text",
            text: `📊 Hasil dari "${expression}" adalah: ${result}`,
            quoteToken: event.message.quoteToken,
          },
        ],
      });
    } catch (error) {
      // Tangani error jika terjadi masalah dalam evaluasi
      client.replyMessage({
        replyToken: event.replyToken,
        messages: [
          {
            type: "text",
            text: `❌ Terjadi kesalahan: ${error.message}`,
            quoteToken: event.message.quoteToken,
          },
        ],
      });
    }
  },
};

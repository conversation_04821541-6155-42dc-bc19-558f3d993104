from flask import Blueprint, request, jsonify
from akinator import Akinator, InvalidChoiceError, CantGoBackAnyFurther
from app.services.akinator_session import sessions
import uuid

akinator_bp = Blueprint('akinator', __name__)

@akinator_bp.route('/start', methods=['POST'])
def start_game():
    session_id = str(uuid.uuid4())
    aki = Akinator()
    aki.start_game()
    sessions[session_id] = aki
    return jsonify({"session_id": session_id, "question": str(aki)})

@akinator_bp.route('/answer', methods=['POST'])
def answer_question():
    data = request.get_json()
    session_id, answer = data.get("session_id"), data.get("answer")
    if session_id not in sessions:
        return jsonify({"error": "Invalid session ID"}), 400

    aki = sessions[session_id]
    try:
        aki.answer(answer)
        if aki.name_proposition:
            result = {
                "guess": {
                    "name_proposition": aki.name_proposition,
                    "description_proposition": aki.description_proposition,
                    "photo": aki.photo
                },
                "finished": True
            }
            del sessions[session_id]
            return jsonify(result)
        return jsonify({"question": str(aki), "step": aki.step, "finished": False})
    except InvalidChoiceError:
        return jsonify({"error": "Invalid answer"}), 400
    except Exception as e:
        del sessions[session_id]
        return jsonify({"error": str(e)}), 500

@akinator_bp.route('/back', methods=['POST'])
def go_back():
    session_id = request.get_json().get("session_id")
    if session_id not in sessions:
        return jsonify({"error": "Invalid session ID"}), 400

    try:
        question = sessions[session_id].back()
        return jsonify({"question": question, "step": sessions[session_id].step})
    except CantGoBackAnyFurther:
        return jsonify({"error": "You can't go back any further"}), 400

@akinator_bp.route('/end', methods=['POST'])
def end_game():
    session_id = request.get_json().get("session_id")
    sessions.pop(session_id, None)
    return jsonify({"message": "Session ended"})

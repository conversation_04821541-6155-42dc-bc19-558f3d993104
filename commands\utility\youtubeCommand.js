const { default: YouTube } = require("youtube-sr");

module.exports = {
  command: "youtube",
  aliases: ["yt"],
  category: "utility",
  description: "Cari video di YouTube",
  requiresPrefix: true,
  includes: false,
  handler: async (client, blobClient, event, args) => {
    const query = args.join(" ");
    if (!query) {
      return client.replyMessage({
        replyToken: event.replyToken,
        messages: [
          { type: "text", 
            text: "Masukkan query pencarian!",
            quoteToken: event.message.quoteToken,
          }
        ],
      });
    }

    try {
      const videos = await YouTube.search(query, { limit: 12 });
      
      const flexContents = videos.map((video) => ({
        type: "bubble",
        size: "hecto",
        hero: {
          type: "image",
          url: `https://to-jpg.vercel.app/convert?url=${video.thumbnail.url}&format=jpg`,
          size: "full",
          aspectRatio: "20:13",
          aspectMode: "cover",
          action: {
            type: "uri",
            uri: video.url, // Clicking thumbnail opens video URL
          },
        },
        body: {
          type: "box",
          layout: "vertical",
          contents: [
            {
              type: "text",
              text: video.title || "No title",
              weight: "bold",
              size: "sm",
              wrap: false,
              action: {
                type: "uri",
                uri: video.url, // Clicking title opens video URL
              },
            },
            {
              type: "box",
              layout: "horizontal",
              margin: "xs",
              contents: [
                {
                  type: "text",
                  text: `Duration: ${video.durationFormatted || "N/A"}`,
                  color: "#999999",
                  size: "sm",
                },
              ],
            },
          ],
        },
        footer: {
          type: "box",
          layout: "vertical",
          spacing: "sm",
          contents: [
            {
              type: "button",
              style: "primary",
              height: "sm",
              action: {
                type: "postback",
                label: "Play as audio",
                data: "audioYoutubeData=" + video.url,
                displayText: `Bot generated :
                
Request : play ${video.title} as audio`,
              },
              color: "#4DA8DA"
            },
            {
              type: "button",
              style: "primary",
              height: "sm",
              action: {
                type: "postback",
                label: "Play as video",
                data: "videoYoutubeData=" + video.url,
                displayText: `Bot generated :
                
Request : play ${video.title} as video`,
              },
              color: "#4DA8DA",
            },
            {
              type: "button",
              style: "secondary",
              height: "sm",
              action: {
                type: "message",
                label: "Get YouTube URL",
                text: "Bot generated : " + video.url,
              },
              color: "#FF6B9D",
            },
          ],
        },
      }));

      client.replyMessage({
        replyToken: event.replyToken,
        messages: [
          {
            type: "text",
            text: "Currently, there is a bug in LINE for Windows where Flex messages sometimes only show plain text instead of the actual Flex components. However, this bug doesn't occur on LINE for Android devices.",
          
          },
          {
            type: "flex",
            altText: "Hasil pencarian YouTube",
            contents: { type: "carousel", contents: flexContents },
          },
        ],
      });
    } catch (error) {
      console.error(error);
      client.replyMessage({
        replyToken: event.replyToken,
        messages: [
          {
            type: "text",
            text: "Terjadi kesalahan saat mencari video YouTube.",
          },
        ],
      });
    }
  },
};

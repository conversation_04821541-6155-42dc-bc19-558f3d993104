const axios = require("axios");
const { QuickDB } = require("quick.db");
const db = new QuickDB();
const fs = require("fs");
const path = require("path");
const { generateApiUrl } = require("../../utils/urlSigner");
const { setSessionTimeout } = require("../../utils/sessionTimeouts");


module.exports = {
  command: "sauce",
  aliases: ["saucenao"],
  category: "anime",
  description: "Search artwork original source by URL",
  requiresPrefix: true, 
  includes: false, 
  handler: async (client, blobClient, event, args) => {
    let imageurl = args.join(" ");
    if (!imageurl && event.message.quotedMessageId) {
      
      const quotedMessageId = event.message.quotedMessageId;
      let stream = await blobClient.getMessageContent(quotedMessageId);
      let chunks = [];

      stream.on("data", (chunk) => chunks.push(chunk));

      const timestamp = Date.now();
      stream.on("end", async () => {
        const buffer = Buffer.concat(chunks);
        const dirPath = path.join(__dirname, "../../static/downloads");
        const filePath = path.join(dirPath, `${timestamp}.jpg`);

        if (!fs.existsSync(dirPath)) {
          fs.mkdirSync(dirPath, { recursive: true });
        }
        fs.writeFileSync(filePath, buffer);

        // Generate API URL for external service access
        const imageUrl = generateApiUrl(`/downloads/${timestamp}.jpg`);

        try {
          const res = await axios.get(
            `https://saucenao.com/search.php?db=999&output_type=2&numres=5&api_key=${process.env.saucenao}&url=${imageUrl}`
          );

          if (!res.data?.results?.length) {
            return client.replyMessage({
              replyToken: event.replyToken,
              messages: [
                { 
                  type: "text", 
                  text: "No similar images found.",
                  quoteToken: event.message.quoteToken,  

                }
              ],
            });
          }

          const sauce = res.data.results[0];
          const extUrls = sauce.data.ext_urls || [];
          const similarity = parseFloat(sauce.header.similarity) 
          const getConfidenceText = (percentage) => {
            if (percentage >= 90) return "🔥 Looks like a perfect match!";
            if (percentage >= 80) return "👌 Pretty close match!";
            if (percentage >= 70) return "🤔 Could be right, but double-check!";
            return "😬 Not too sure about this one...";
          };

          const formatKey = (key) =>
            key.replace(/_/g, " ").replace(/\b\w/g, (c) => c.toUpperCase());
  
          // Dynamically format `sauce.data`
          const formattedData = Object.entries(sauce.data)
        .filter(([key, value]) => value && key !== "ext_urls")
        .map(([key, value]) => `${formatKey(key)}: ${value}`)
        .join("\n");

          const replyText = `${getConfidenceText(similarity)}\n\nSimilarity: ${sauce.header.similarity}%\n${formattedData}\n` +
            "===========================\n" +
            (extUrls.length ? "External URLs:\n- " + extUrls.join("\n- ") : "");

          await client.replyMessage({
            replyToken: event.replyToken,
            messages: [
              { type: "text",
                text: replyText,
                quoteToken: event.message.quoteToken,  
              }
            ],
          });

        } catch (error) {
          console.error("Error in SauceNAO request:", error);
          client.replyMessage({
            replyToken: event.replyToken,
            messages: [
              { 
                type: "text", 
                text: "An error occurred while processing the image.",
                quoteToken: event.message.quoteToken,  

              }
            ],
          });
        }
      });

      stream.on("error", async (err) => {
        console.error("Error downloading content:", err);
        client.replyMessage({
          replyToken: event.replyToken,
          messages: [
            { 
              type: "text",
              text: "An error occurred while downloading the image.",
              quoteToken: event.message.quoteToken,  
            }
          ],
        });
      });

    } else if (!imageurl) {
      
      const userId = event.source.userId;
      await db.set(`saucenao_${userId}_active`, true);
      await client.replyMessage({
        replyToken: event.replyToken,
        messages: [
          { 
            type: "text", 
            text: "Please provide an image URL or upload an image now.",
            quoteToken: event.message.quoteToken,  
          }
        ],
      });
      setSessionTimeout(userId, async () => {
        console.log(`SauceNAO session for user ${userId} timed out.`);
        await db.delete(`saucenao_${userId}_active`);
      });
    } else {
      try {
        const res = await axios.get(
          `https://saucenao.com/search.php?db=999&output_type=2&numres=5&api_key=${process.env.saucenao}&url=${imageurl}`
        );

        if (!res.data?.results?.length) {
          return client.replyMessage({
            replyToken: event.replyToken,
            messages: [
              { type: "text", 
                text: "No sauce found for the provided image.",
                quoteToken: event.message.quoteToken,  

              }],
          });
        }

        const sauce = res.data.results[0];
        const extUrls = sauce.data.ext_urls || [];
        const similarity = parseFloat(sauce.header.similarity) 
        const getConfidenceText = (percentage) => {
          if (percentage >= 90) return "🔥 Looks like a perfect match!";
          if (percentage >= 80) return "👌 Pretty close match!";
          if (percentage >= 70) return "🤔 Could be right, but double-check!";
          return "😬 Not too sure about this one...";
        };
        const formatKey = (key) =>
          key.replace(/_/g, " ").replace(/\b\w/g, (c) => c.toUpperCase());

        // Dynamically format `sauce.data`
        const formattedData = Object.entries(sauce.data)
      .filter(([key, value]) => value && key !== "ext_urls")
      .map(([key, value]) => `${formatKey(key)}: ${value}`)
      .join("\n");

        const replyText = `${getConfidenceText(similarity)}\n\nSimilarity: ${sauce.header.similarity}%\n${formattedData}\n` +
          "===========================\n" +
          (extUrls.length ? "External URLs:\n- " + extUrls.join("\n- ") : "");

        client.replyMessage({
          replyToken: event.replyToken,
          messages: [
            { 
              type: "text", 
              text: replyText,
              quoteToken: event.message.quoteToken,  
            }
          ],
        });

      } catch (error) {
        console.error("Error in SauceNAO request:", error);
        client.replyMessage({
          replyToken: event.replyToken,
          messages: [
            { type: "text", 
              text: "An error occurred while searching for sauce.",
              quoteToken: event.message.quoteToken,  
            }
          ],
        });
      }
    }
  },
};

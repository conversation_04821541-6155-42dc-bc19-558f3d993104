const { exec } = require("child_process");

module.exports = {
  command: "restart",
  aliases: ["rst"],
  category: "misc",
  description: "Restart the bot",
  requiresPrefix: true,
  includes: false,
  handler: async (client, blobClient, event, args) => {
    const replyToken = event.replyToken;

    try {
      // Periksa apakah pengguna memiliki izin
      if (event.source.userId !== process.env.tama) {
        return client.replyMessage({
          replyToken,
          messages: [
            {
              type: "text",
              text: "You don't have the power to control me!",
              quoteToken: event.message.quoteToken,
            },
          ],
        });
      }

      // <PERSON><PERSON> pesan konfirmasi kepada pengguna sebelum restart
      await client.replyMessage({
        replyToken,
        messages: [
          {
            type: "text",
            text: "Restarting... Please wait for 5 seconds.",
            quoteToken: event.message.quoteToken,
          },
        ],
      });

      // Restart bot menggunakan PM2 dari instalasi lokal
      exec("npx pm2 restart vajrabot", (error, stdout, stderr) => {
        if (error) {
          console.error(`Error during restart: ${error.message}`);
          return client.replyMessage({
            replyToken,
            messages: [
              {
                type: "text",
                text: "Failed to restart the bot. Please try again later.",
                quoteToken: event.message.quoteToken,
              },
            ],
          });
        }
        if (stderr) {
          console.error(`Stderr: ${stderr}`);
        }
        console.log(`Stdout: ${stdout}`);
      });
    } catch (err) {
      // Tangani kesalahan jika terjadi
      console.error(`Failed to restart the bot: ${err.message}`);
    }
  },
};

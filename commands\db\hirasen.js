const { QuickDB } = require("quick.db");
const fs = require('fs');
const path = require('path');
const db = new QuickDB();
const { setSessionTimeout } = require('../../utils/sessionTimeouts');
const datasetPath = path.join(__dirname, '../../utils/hiragana_dataset.json');
let dataset = [];
try {
  dataset = JSON.parse(fs.readFileSync(datasetPath, 'utf8'));
} catch (error) {
  console.error("Error loading Hiragana dataset:", error);
  dataset = [];
}

function shuffleArray(array) {
  let currentIndex = array.length, randomIndex;
  while (currentIndex !== 0) {
    randomIndex = Math.floor(Math.random() * currentIndex);
    currentIndex--;
    [array[currentIndex], array[randomIndex]] = [array[randomIndex], array[currentIndex]];
  }
  return array;
}

function getRandomQuizSet(count = 4) {
  if (!dataset || dataset.length < count) {
    return null;
  }
  const shuffled = shuffleArray([...dataset]);
  return shuffled.slice(0, count);
}

// Now includes player name and uses English, no markdown
function generateQuestionPayload(gameState) {
    const sentenceList = getRandomQuizSet(4);
    if (!sentenceList) {
        return { error: "Failed to load quiz data." };
    }
    if (!gameState || !gameState.playerName) {
        return { error: "Player data missing." };
    }

    const current = sentenceList[0];
    const correctTranslation = current.translation;
    const distractors = sentenceList.slice(1).map(item => item.translation);
    const allOptions = shuffleArray([correctTranslation, ...distractors]);
    const letters = ['A', 'B', 'C', 'D'];
    const correctIndex = allOptions.indexOf(correctTranslation);
    const correctLetter = letters[correctIndex];

    const healthDisplay = gameState.health > 0 ? '💖'.repeat(gameState.health) : '💔';
    // English text, player name, no markdown
    const questionText = `🌀 Hiragana Sentence Quiz 🌀\n\n${gameState.playerName}, what does this sentence mean?\n\n📝 ${current.sentence}\n\nOptions:\n${allOptions.map((opt, i) => `${letters[i]}) ${opt}`).join('\n')}\n\nType your answer (A/B/C/D) or 'Stop'.\n\nHealth: ${healthDisplay}\nScore: ${gameState.score}`;

    return {
        questionText,
        correctLetter,
        correctTranslation
    };
}

module.exports = {
  command: "hirasen",
  aliases: ["hisen"],
  category: "game",
  description: "Practice Hiragana sentence reading with a translation quiz!", // English
  requiresPrefix: true,
  includes: false,
  handler: async (client, blobClient, event, args) => {
    const userId = event.source.userId;
    const groupId = event.source.groupId || event.source.roomId || userId;

    const isGameActive = await db.get(`${groupId}_hirasenActive`);
    if (isGameActive) {
      return client.replyMessage({
        replyToken: event.replyToken,
        messages: [{
          type: "text",
          text: "A Hiragana quiz is already active here. Please wait or type 'Stop'.", // English
          quoteToken: event.message.quoteToken,
        }],
      });
    }

    if (!dataset || dataset.length < 4) {
       return client.replyMessage({
        replyToken: event.replyToken,
        messages: [{
          type: "text",
          text: "Sorry, the Hiragana quiz data is insufficient or could not be loaded. Cannot start the game.", // English
          quoteToken: event.message.quoteToken,
        }],
      });
    }

    // Get player profile
    let playerName = "Player";
    try {
        const profile = await client.getGroupMemberProfile(groupId, userId);
        playerName = profile.displayName;
    } catch (profileError) {
        console.error(`Failed to get profile for ${userId}:`, profileError);
    }

    const initialHealth = Math.min(parseInt(args[0]) || 3, 7);

    let initialState = {
      score: 0,
      health: initialHealth,
      playerName: playerName, // Store player name
      stopped: false,
      correctLetter: null,
      correctTranslation: null,
    };

    const firstQuestionPayload = generateQuestionPayload(initialState); // Pass initial state with name

    if (firstQuestionPayload.error) {
         return client.replyMessage({
            replyToken: event.replyToken,
            messages: [{ type: "text", text: firstQuestionPayload.error }],
        });
    }

    initialState.correctLetter = firstQuestionPayload.correctLetter;
    initialState.correctTranslation = firstQuestionPayload.correctTranslation;

    await db.set(`${groupId}_hirasenActive`, true);
    await db.set(`${groupId}_hirasenOwner`, userId);
    await db.set(`${groupId}_hirasenState`, initialState);

    client.replyMessage({
      replyToken: event.replyToken,
      messages: [
        {
          type: "text",
          text: firstQuestionPayload.questionText,
        }
      ]
    });
    setSessionTimeout(groupId, async () => {
        console.log(`Hiragana Quiz session for group ${groupId} timed out.`);
        await db.delete(`${groupId}_hirasenActive`);
        await db.delete(`${groupId}_hirasenOwner`);
        await db.delete(`${groupId}_hirasenState`);
    });
  },
  generateQuestionPayload,
};
module.exports = {
  command: "pilih",
  aliases: ["choose"],
  category: "fun",
  description: "Males milih, suruh bot saja, \nexample : !pilih touchgrass;touchbed",
  requiresPrefix: true, 
  includes: false, 
  handler: (client, blobClient, event, args) => {
    let optionString = args.join(" ");
    if (!optionString.includes(';')) return client.replyMessage(event.replyToken, {
      type: "text",
      text: "Hæh?",
      quoteToken: event.message.quoteToken,
    });
    let options = optionString.split(";");
    if (options.length < 2)
      return client.replyMessage({
        replyToken: event.replyToken,
        messages: [
          {
            type: "text",
            text: "<PERSON><PERSON> man, pake commandnya yang bener",
            quoteToken: event.message.quoteToken,
          },
        ],
      });
    let answers = options[Math.floor(Math.random() * options.length)];

    client.replyMessage({
      replyToken: event.replyToken,
      messages: [
        {
          type: "text",
          text: `🤔 Aku pilih... ${answers}`,
          quoteToken: event.message.quoteToken, // Reply to the message
        },
      ],
    });
  },
};

const axios = require("axios");

module.exports = {
  command: "mock",
  aliases: ["spongebob", "sponge"],
  category: "fun",
  description: "Spongebob mock 🐔🐔",
  requiresPrefix: true, // Set to false to enable prefix-less activation
  includes: false,
  handler: async (client, blobClient, event, args) => {
    // Fungsi untuk mengubah teks menjadi mock text (huruf besar dan kecil acak)
    const mockText = (text) => {
      return text
        .split("")
        .map((char) =>
          Math.random() > 0.5 ? char.toUpperCase() : char.toLowerCase()
        )
        .join("");
    };

    // Ambil teks dari args dan ubah menjadi mock text
    const text = args.join(" ");
    const mockedText = mockText(text);

    try {
      // Membuat parameter form dalam format URL-encoded
      const params = new URLSearchParams();
      params.append("template_id", "102156234"); // Template ID untuk Spongebob Mock meme
      params.append("username", process.env.IMGFLIP_USERNAME);
      params.append("password", process.env.IMGFLIP_PASSWORD);
      params.append("boxes[0][text]", mockedText);

      // Kirim permintaan ke Imgflip API
      const response = await axios.post(
        "https://api.imgflip.com/caption_image",
        params,
        {
          headers: { "Content-Type": "application/x-www-form-urlencoded" },
        }
      );

      // Jika berhasil, kirim gambar meme ke LINE
      if (response.data.success) {
        const memeUrl = response.data.data.url;

        await client.replyMessage({
          replyToken: event.replyToken,
          messages: [
            {
              type: "image",
              previewImageUrl: memeUrl,
              originalContentUrl: memeUrl,
            },
          ],
        });
      } else {
        throw new Error(response.data.error_message);
      }
    } catch (error) {
      console.error("Error creating meme:", error.message);
      await client.replyMessage({
        replyToken: event.replyToken,
        messages: [
          {
            type: "text",
            text: `Gagal membuat meme: ${error.message}`,
            quoteToken: event.message.quoteToken,
          },
        ],
      });
    }
  },
};

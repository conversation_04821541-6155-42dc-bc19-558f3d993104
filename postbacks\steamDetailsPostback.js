const axios = require("axios");
const { TruncateText } = require("../utils/utils");

module.exports = {
  postbackData: "steamData=",
  cooldown: 5,
  handler: async (client, blobClient, event, id) => {
    try {
      // Fetch game details from Steam API
      const response = await axios.get(
        `https://store.steampowered.com/api/appdetails/?cc=id&appids=${id}`
      );

      const gameDetails = response.data[id].data;

      if (!gameDetails) {
        return client.replyMessage({
          replyToken: event.replyToken,
          messages: [
            {
              type: "text",
              text: "Cannot find details for this game.",
            },
          ],
        });
      }

      // Prepare the message content
      const gameImage = gameDetails.header_image;
      const gameName = gameDetails.name || "Game Name Not Found";

      // Remove HTML tags and truncate the description
      const rawDescription =
        gameDetails.short_description || "Description not available.";
      const gameDescription = TruncateText(
        rawDescription.replace(/<[^>]+>/g, ""),
        2000
      );
      // Handle pricing information
      const priceOverview = gameDetails.price_overview || {};
      const originalPrice = priceOverview.initial
        ? `Rp ${Number(priceOverview.initial / 100).toLocaleString("id-ID")}`
        : "Free";
      const discountPrice =
        priceOverview.final && priceOverview.discount_percent > 0
          ? `Rp ${Number(priceOverview.final / 100).toLocaleString(
              "id-ID"
            )} (Diskon: ${priceOverview.discount_percent}%)`
          : "No discount";

      // Clean up minimum requirements using regex for formatting
      const minimumRequirementsHTML = gameDetails.pc_requirements
        ? gameDetails.pc_requirements.minimum
        : "No minimum requirements.";
      const minimumRequirements = minimumRequirementsHTML
        .replace(/<\/?li>/g, "\n") // Replace <li> and </li> with line breaks
        .replace(/<[^>]+>/g, "") // Remove all other HTML tags
        .replace(/\n{2}/g, '\n')
        .trim();
      const categories =
        gameDetails.categories.map((cat) => cat.description).join(", ") ||
        "No categories.";
      const developer = gameDetails.developers.join(", ") || "No developer.";
      const publisher = gameDetails.publishers.join(", ") || "No publisher.";
      const genres =
        gameDetails.genres.map((genre) => genre.description).join(", ") ||
        "No genres.";
      const releaseDate =
        gameDetails.release_date.date || "Release date not available.";

      // Create reply text
      const replyText =
        gameName +
        "\n\n" +
        gameDescription +
        "\n\n" +
        "Harga Asli: " +
        originalPrice +
        "\n" +
        "Harga Diskon: " +
        discountPrice +
        "\n\n" +
        "Requirements:\n" +
        minimumRequirements +
        "\n\n" +
        "Categories: " +
        categories +
        "\n" +
        "Developer: " +
        developer +
        "\n" +
        "Publisher: " +
         publisher +
        "\n"
        "Genre: " +
        genres +
        "\n" +
        "Release Date: " +
        releaseDate;

      // Send the details message
      await client.replyMessage({
        replyToken: event.replyToken,
        messages: [
          {
            type: "image",
            originalContentUrl: gameImage,
            previewImageUrl: gameImage,
          },
          {
            type: "text",
            text: replyText,
          },
        ],
      });
    } catch (error) {
      console.error("Error fetching game details from Steam API:", error);
      await client.replyMessage({
        replyToken: event.replyToken,
        messages: [
          {
            type: "text",
            text: "An error occurred while fetching game details. Please try again later.",
          },
        ],
      });
    }
  },
};

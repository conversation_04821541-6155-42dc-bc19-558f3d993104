const { QuickDB } = require("quick.db");
const axios = require("axios");
const db = new QuickDB();

module.exports = {
  command: "clearchat",
  aliases: ["clearchat", "resetchat", "clearhistory"],
  category: "utility",
  description: "Menghapus riwayat percakapan chat dengan bot",
  requiresPrefix: true,
  includes: false,
  handler: async (client, blobClient, event, args) => {
    let userId = event.source.userId;
    if (event.source.type === "group") {
      userId = `${event.source.groupId}:${event.source.userId}`;
    }

    try {
      // Get user info for personalized response
      let user;
      if (event.source.type === "group") {
        user = await client.getGroupMemberProfile(event.source.groupId, event.source.userId);
      } else {
        user = await client.getProfile(event.source.userId);
      }

      // Check if user has chat history (check both storage keys)
      const existingHistory = await db.get(`chatHistory_${userId}`);
      const existingHistoryV2 = await db.get(`lastMessage_${userId}`);

      if ((!existingHistory || existingHistory.length === 0) &&
          (!existingHistoryV2 || existingHistoryV2.length === 0)) {
        await client.replyMessage({
          replyToken: event.replyToken,
          messages: [
            {
              type: "text",
              text: "❌ Tidak ada riwayat chat yang ditemukan untuk dihapus.",
              quoteToken: event.message.quoteToken,
            }
          ]
        });
        return;
      }

      // Clear both chat history storage keys (Node.js QuickDB)
      await db.delete(`chatHistory_${userId}`);
      await db.delete(`lastMessage_${userId}`);

      // Also try to clear Python SQLite database (if available)
      try {
        await axios.post("http://localhost:5000/api/gemini/clear-history", {
          user_id: userId
        });
        console.log(`Python SQLite chat history also cleared for user ${userId}`);
      } catch (pythonError) {
        // Python API might not be available or using different endpoint, that's okay
        console.log(`Python API not available or error clearing Python history: ${pythonError.message}`);
      }

      // Send confirmation message
      await client.replyMessage({
        replyToken: event.replyToken,
        messages: [
          {
            type: "text",
            text: `✅ Riwayat chat berhasil dihapus, ${user.displayName}!\n\n📝 Yang dihapus:\n• Riwayat chat Node.js (QuickDB)\n• Riwayat chat Python (SQLite)\n\nPercakapan baru akan dimulai dari awal tanpa konteks sebelumnya.`,
            quoteToken: event.message.quoteToken,
          }
        ]
      });

      console.log(`All chat history cleared for user ${userId} (${user.displayName}) - Node.js QuickDB and Python SQLite`);

    } catch (error) {
      console.error("Error clearing chat history:", error);
      await client.replyMessage({
        replyToken: event.replyToken,
        messages: [
          { 
            type: "text", 
            text: "❌ Terjadi kesalahan saat menghapus riwayat chat. Silakan coba lagi.",
            quoteToken: event.message.quoteToken,
          }
        ]
      });
    }
  }
};

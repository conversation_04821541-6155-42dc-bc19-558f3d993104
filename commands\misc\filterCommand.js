const { audioFilterMap, videoFilterMap } = require("../../utils/filters");

module.exports = {
  command: "filter",
  aliases: [],
  category: "misc",
  description: "Shows available filters for !mp3 and !mp4 commands",
  requiresPrefix: true, // Set to false to enable prefix-less activation
  includes: false, // Set to true if the command should trigger when included in a message
  handler: async (client, blobClient, event, args) => {

    const audioFilters = Object.keys(audioFilterMap);
    const videoFilters = Object.keys(videoFilterMap);

    client.replyMessage({
      replyToken: event.replyToken,
      messages: [
        {
          type: "text",
          text: `🎵 AUDIO FILTERS for "!mp3" command:

Supported filters: ${audioFilters.join(", ")}.

Usage Examples:
!mp3 https://www.youtube.com/watch?v=dQw4w9WgXcQ [distortion,chipmunk]

Custom filter example:
!mp3 https://www.youtube.com/watch?v=dQw4w9WgXcQ [custom=asetrate=44100*1.5,aresample=44100]

Custom + Preset example:
!mp3 https://www.youtube.com/watch?v=dQw4w9WgXcQ [bassboost,custom=volume=2.0]

⚠️ Note: Audio filters only work with !mp3 command.
Filters must be placed inside [ ].`,
          quoteToken: event.message.quoteToken,
        },
        {
          type: "text",
          text: `🎬 VIDEO FILTERS for "!mp4" command:

Supported filters: ${videoFilters.join(", ")}.

Usage Examples:
!mp4 https://www.youtube.com/watch?v=dQw4w9WgXcQ [vintage,blur]

Custom filter example:
!mp4 https://www.youtube.com/watch?v=dQw4w9WgXcQ [custom=brightness=1.5]

Custom + Preset example:
!mp4 https://www.youtube.com/watch?v=dQw4w9WgXcQ [sepia,custom=contrast=1.2]

⚠️ Note: Video filters only work with !mp4 command.
Filters must be placed inside [ ].

📖 More about FFMpeg filters: https://ffmpeg.org/ffmpeg-filters.html`,
          quoteToken: event.message.quoteToken,
        }
      ]
    });

  }
};
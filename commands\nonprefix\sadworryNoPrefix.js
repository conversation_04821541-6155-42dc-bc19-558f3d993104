const { generateSecureUrl } = require("../../utils/urlSigner");

module.exports = {
  command: "sadworry",
  aliases: [],
  category: "nonprefix",
  description: "Gesek kartu ajah",
  requiresPrefix: false,
  includes: false,
  handler: async (client, blobClient, event, args) => {

    const user = await client.getGroupMemberProfile(event.source.groupId, event.source.userId)

    // Generate secure URL for the image
    const imageUrl = generateSecureUrl("/image/sadworry.png");

    client.replyMessage({
      replyToken: event.replyToken,
      messages: [
        {
          type: "image",
          originalContentUrl: imageUrl,
          previewImageUrl: imageUrl,
          sender: {
            iconUrl: user.pictureUrl,
          },
        },
      ],
    });
  },
};

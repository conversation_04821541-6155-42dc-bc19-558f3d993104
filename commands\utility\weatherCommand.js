const axios = require("axios");
const { format, parseISO } = require("date-fns");
const { id } = require("date-fns/locale");

module.exports = {
  command: "cuaca",
  aliases: ["weather"],
  category: "utility",
  description: "Menampilkan informasi cuaca terkini",
  requiresPrefix: true,
  includes: false,
  handler: async (client, blobClient, event, args) => {
    const location = args.join(" ");
    if (!location) {
      return client.replyMessage({
        replyToken: event.replyToken,
        messages: [
          {
            type: "text",
            text: "❌ Harap berikan nama lokasi!",
          },
        ],
      });
    }

    const getWeatherData = async (url) => {
      try {
        const response = await axios.get(url);
        return response.data;
      } catch (error) {
        if (error.response && error.response.status === 503) {
          return null;
        } else {
          throw error;
        }
      }
    };

    try {
      // First attempt with %Z (including timezone)
      let data = await getWeatherData(
        `https://wttr.in/${encodeURI(location)}?format=%C|%t|%w|%h|%p|%Z`
      );

      // If the response was null due to 503 error, try without timezone
      let timezone = "";
      if (!data) {
        data = await getWeatherData(
          `https://wttr.in/${encodeURI(location)}?format=%C|%t|%w|%h|%p`
        );
      } else {
        // Extract timezone from first response if available
        timezone = data.split("|")[5]?.trim();
      }

      // Split the data response
      const weatherData = data.split("|");
      const [condition, temperature, wind, humidity, precipitation] =
        weatherData;

      // Display local time if timezone information available
      const localTime = timezone
        ? new Date().toLocaleTimeString("id-ID", {
            hour: "2-digit",
            minute: "2-digit",
            timeZone: timezone,
          })
        : null;

      // Get date information in JSON format and format it
      const dateResponse = await axios.get(
        `https://wttr.in/${encodeURI(location)}?format=j1`
      );
      const rawDate = dateResponse.data.weather[0].date;
      const formattedDate = format(parseISO(rawDate), "EEEE, dd MMMM yyyy", {
        locale: id,
      });

      // Prepare Flex message
      const flexMessageContents = [
        {
          type: "text",
          text: `🌏 Cuaca di ${
            location.charAt(0).toUpperCase() + location.slice(1).toLowerCase()
          }`,
          weight: "bold",
          size: "lg",
          align: "center",
          color: "#1E88E5",
          wrap: true,
        },
        {
          type: "text",
          text: `📅 ${formattedDate}`,
          size: "sm",
          align: "center",
          color: "#666666",
          margin: "sm",
        },
        localTime && {
          type: "text",
          text: `🕒 ${localTime} (Waktu Lokal)`,
          size: "sm",
          align: "center",
          color: "#666666",
          margin: "sm",
        },
        {
          type: "text",
          text: condition || "N/A",
          size: "md",
          align: "center",
          margin: "md",
          color: "#555555",
        },
        {
          type: "text",
          text: temperature || "N/A",
          size: "lg",
          align: "center",
          margin: "md",
          color: "#FF7043",
        },
        {
          type: "box",
          layout: "horizontal",
          contents: [
            {
              type: "text",
              text: "💨 Angin",
              size: "xs",
              color: "#1E88E5",
              flex: 1,
            },
            {
              type: "text",
              text: wind || "N/A",
              size: "xs",
              align: "end",
              color: "#555555",
              flex: 1,
            },
          ],
          margin: "md",
        },
        {
          type: "box",
          layout: "horizontal",
          contents: [
            {
              type: "text",
              text: "💧 Kelembaban",
              size: "xs",
              color: "#1E88E5",
              flex: 1,
            },
            {
              type: "text",
              text: humidity || "N/A",
              size: "xs",
              align: "end",
              color: "#555555",
              flex: 1,
            },
          ],
          margin: "sm",
        },
        {
          type: "box",
          layout: "horizontal",
          contents: [
            {
              type: "text",
              text: "🌧️ Curah Hujan",
              size: "xs",
              color: "#1E88E5",
              flex: 1,
            },
            {
              type: "text",
              text: precipitation || "N/A",
              size: "xs",
              align: "end",
              color: "#555555",
              flex: 1,
            },
          ],
          margin: "sm",
        },
      ].filter(Boolean);

      const flexMessage = {
        type: "flex",
        altText: `Cuaca di ${location}`,
        contents: {
          type: "bubble",
          size: "hecto",
          body: {
            type: "box",
            layout: "vertical",
            contents: flexMessageContents,
          },
        },
      };

      await client.replyMessage({
        replyToken: event.replyToken,
        messages: [flexMessage],
      });
    } catch (error) {
      console.error("Error fetching weather data:", error);
      client.replyMessage({
        replyToken: event.replyToken,
        messages: [
          {
            type: "text",
            text: "Error saat mencari data cuaca. Silakan coba lagi nanti.",
          },
        ],
      });
    }
  },
};
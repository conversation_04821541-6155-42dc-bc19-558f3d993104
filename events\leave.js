const { QuickDB } = require("quick.db");
const db = new QuickDB();
const { clearSessionTimeout } = require("../utils/sessionTimeouts");

module.exports = async (event, client, blobClient) => {
  const groupId = event.source.groupId;
  if (groupId) {
    console.log(`<PERSON><PERSON> is leaving group ${groupId}, cleaning up active sessions.`);

    // Clear all potential game sessions for the group
    await db.delete(`${groupId}_triviaActive`);
    await db.delete(`${groupId}_triviaOwner`);
    await db.delete(`${groupId}_triviaState`);

    await db.delete(`${groupId}_akinatorActive`);
    await db.delete(`${groupId}_akinatorOwner`);
    await db.delete(`${groupId}_akinatorSession`);
    await db.delete(`${groupId}_akinatorStep`);

    await db.delete(`${groupId}_wordguessActive`);
    await db.delete(`${groupId}_wordguessOwner`);
    await db.delete(`${groupId}_wordguessState`);

    await db.delete(`${groupId}_hirasenActive`);
    await db.delete(`${groupId}_hirasenOwner`);
    await db.delete(`${groupId}_hirasenState`);

    // Clear any active timeouts for the group
    clearSessionTimeout(groupId);
  }
  console.log(`I'm leaving`);
};

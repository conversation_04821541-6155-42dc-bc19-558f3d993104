module.exports = {
  command: "coopterms",
  aliases: ['ct'],
  category: "nonprefix",
  description: "Room Coop name",
  requiresPrefix: false, 
  includes: false, 
  handler: async (client, blobClient, event, args) => {

    const replyText =
      "Common Hexa (room) : 天元＠風水土火光闇 \n" +
      "Common Pajero (room) : ルシゼロ＠風水土火光闇 \n\n" +
      "Hexa : 天元 (Tengen) \n" +
      "Pajero : ルシゼロ (Rushizero) \n\n" +
      "Elemental :\n" +
      "Wind : 風 \n" +
      "Fire : 火 \n" +
      "Earth : 土 \n" +
      "Water : 水 \n" +
      "Light : 光 \n" +
      "Darkness : 闇 \n\n" +
      "Etc :\n" +
      "Host (You) : 主\n" +
      "Practice : 練習\n\n" +
      "Example : ルシゼロ主闇練習＠風水土火光\n" +
      "= Host practice pajero (using) dark, need Wind, Water, Earth, Fire, Light";

    client.replyMessage({
      replyToken: event.replyToken,
      messages: [
        {
          type: "text",
          text: replyText,
          quoteToken: event.message.quoteToken
        }
      ]
    });

  }
}
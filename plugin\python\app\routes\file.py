from flask import Blueprint, request, jsonify
from google import genai
import requests, os, mimetypes
from urllib.parse import urlparse

file_bp = Blueprint('file', __name__)
genai_client = genai.Client(api_key=os.getenv("GOOGLE_API_KEY"))

@file_bp.route('/analyze_file', methods=['POST'])
def analyze_file():
    try:
        data = request.get_json()
        file_url = data.get("file_url")
        question = data.get("question", "What is in this file?")
        if not file_url:
            return jsonify({"error": "File URL is required"}), 400

        file_data = requests.get(file_url).content
        
        # Guess the extension from the URL
        content_type = requests.head(file_url).headers.get('content-type')
        extension = mimetypes.guess_extension(content_type)
        if not extension:
            # fallback to url parsing
            parsed = urlparse(file_url)
            path = parsed.path  # gives /attachments/.../filename.json
            extension = os.path.splitext(path)[1]  # gives .json cleanly

        temp_file_name = f"temp_file{extension}"

        with open(temp_file_name, "wb") as f:
            f.write(file_data)

        uploaded_file = genai_client.files.upload(file=temp_file_name)
        response = genai_client.models.generate_content(
            model="gemini-2.0-flash",
            contents=[uploaded_file, question]
        )
        return jsonify({"answer": response.text})
    except Exception as e:
        return jsonify({"error": str(e)}), 500
    finally:
        if os.path.exists(temp_file_name):
            os.remove(temp_file_name)
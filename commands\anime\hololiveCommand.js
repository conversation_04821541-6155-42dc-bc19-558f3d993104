const axios = require("axios");
const {  TZDateMini } = require('@date-fns/tz');
const { id } = require('date-fns/locale');
const {format} = require('date-fns');

module.exports = {
  command: "hololive",
  aliases: ["holo"],
  category: "anime",
  description: "Lihat member Hololive yang sedang live",
  requiresPrefix: true,
  includes: false,
  handler: async (client, blobClient, event, args) => {
    try {
      const HOLODEX_API_KEY = process.env.HOLODEX_API_KEY;

      // Mengambil data live stream Hololive dari Holodex API
      const response = await axios.get("https://holodex.net/api/v2/live", {
        headers: {
          "X-APIKEY": HOLODEX_API_KEY,
        },
        params: {
          org: "Hololive",
          limit: 12, // Maksimal 12 live streams
          status: "live",
        },
      });
      const formatStreamTime = (dateString) => {
        try {
          // Create TZDate instance for Jakarta timezone
          const jakartaDate = new TZDateMini(dateString, "Asia/Jakarta");
          
          // Format using the Jakarta timezone context
          return format(jakartaDate, "dd MMM yyyy, HH:mm", {
            locale: id,
            in: { timeZone: "Asia/Jakarta" }
          });
        } catch (error) {
          console.error("Date formatting error:", error);
          return "Waktu tidak tersedia";
        }
      };
      
      const liveStreams = response.data;

      // Jika tidak ada yang live
      if (!liveStreams || liveStreams.length === 0) {
        return await client.replyMessage({
          replyToken: event.replyToken,
          messages: [
            {
              type: "text",
              text: "Tidak ada member Hololive yang sedang live saat ini.",
              quoteToken: event.message.quoteToken,
            },
          ],
        });
      }

      // Membuat Flex Message bubbles
      const bubbles = liveStreams.map((stream) => ({
        type: "bubble",
        size: "hecto",
        hero: {
          type: "image",
          url: `https://i.ytimg.com/vi/${stream.id}/maxresdefault.jpg`,
          size: "full",
          aspectMode: "cover",
          aspectRatio: "160:90",
        },
        body: {
          type: "box",
          layout: "vertical",
          contents: [
            {
              type: "text",
              text:
                stream.channel.english_name ||
                stream.channel.name ||
                "Hololive Member",
              weight: "bold",
              size: "lg",
              wrap: true,
              color: "#1DB954",
            },
            {
              type: "text",
              text: stream.title || "No Title Available",
              wrap: true,
              size: "md",
              margin: "sm",
              color: "#000000",
            },
            {
              type: "box",
              layout: "baseline",
              margin: "md",
              contents: [
                {
                  type: "icon",
                  url: "https://cdn-icons-png.flaticon.com/512/1384/1384060.png",
                  size: "sm",
                },
                {
                  type: "text",
                  text: `Status: ${stream.status.toUpperCase()}`,
                  color: "#AAAAAA",
                  size: "sm",
                  margin: "xs",
                },
              ],
            },
            {
              type: "text",
              text: `Tayang: ${formatStreamTime(stream.available_at)} WIB`,
              size: "sm",
              color: "#00BFFF",
              margin: "xs",
            },
            // Menambahkan live_viewers dan topic_id
            {
              type: "text",
              text: `Penonton: ${stream.live_viewers || "N/A"}`,
              size: "sm",
              color: "#00BFFF",
              margin: "xs",
            },
            {
              type: "text",
              text: `Topic ID: ${stream.topic_id || "N/A"}`,
              size: "sm",
              color: "#AAAAAA",
              margin: "xs",
            },
          ],
        },
        footer: {
          type: "box",
          layout: "vertical",
          spacing: "sm",
          contents: [
            {
              type: "button",
              style: "primary",
              color: "#FF4500",
              action: {
                type: "uri",
                label: "Tonton Stream",
                uri: `https://www.youtube.com/watch?v=${stream.id}`,
              },
            },
            {
              type: "button",
              style: "link",
              action: {
                type: "uri",
                label: "Lihat Channel",
                uri: `https://www.youtube.com/channel/${stream.channel.id}`,
              },
            },
          ],
        },
      }));

      // Membuat Flex Message carousel
      const flexMessage = {
        type: "flex",
        altText: "Member Hololive yang sedang live",
        contents: {
          type: "carousel",
          contents: bubbles,
        },
      };

      // Mengirimkan Flex Message sebagai balasan
      await client.replyMessage({
        replyToken: event.replyToken,
        messages: [flexMessage],
      });
    } catch (error) {
      console.error("Gagal mendapatkan data Hololive:", error);
      await client.replyMessage({
        replyToken: event.replyToken,
        messages: [
          {
            type: "text",
            text: "Terjadi kesalahan saat mengambil data Hololive.",
            quoteToken: event.message.quoteToken,
          },
        ],
      });
    }
  },
};
